#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试随机起始位置功能
验证智能体是否在不同的x坐标位置生成
"""

import numpy as np
import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms/environments/single_agent_env'))

from custom_parking import CustomParkingEnv

class RandomSpawnConfig:
    """随机生成测试配置类"""
    def __init__(self):
        # 基本环境配置
        self.env_seed = None  # 不固定种子，确保随机性
        self.max_episode_steps = 20
        self.render_mode = 'human'
        
        # 其他配置
        self.collision_reward = -10

def test_random_spawn_positions():
    """测试随机起始位置功能"""
    print("=== 随机起始位置测试 ===\n")
    
    print("测试说明:")
    print("- 智能体的y坐标固定为0（停车场中央通道）")
    print("- x坐标在-25到+25范围内随机生成")
    print("- 确保不会太靠近墙壁（留3米安全距离）")
    print("- 朝向完全随机（0-360度）")
    print()
    
    # 创建配置
    config = RandomSpawnConfig()
    
    # 记录多次重置的起始位置
    spawn_positions = []
    spawn_headings = []
    
    try:
        # 创建环境
        env = CustomParkingEnv(config)
        
        print("进行10次环境重置，记录起始位置...")
        
        for episode in range(10):
            # 重置环境
            obs, info = env.reset()
            
            # 获取车辆位置
            if env.controlled_vehicles:
                vehicle = env.controlled_vehicles[0]
                x, y = vehicle.position
                heading = vehicle.heading
                
                spawn_positions.append((x, y))
                spawn_headings.append(heading)
                
                print(f"Episode {episode + 1:2d}: 位置=({x:6.2f}, {y:6.2f}), 朝向={np.degrees(heading):6.1f}°")
                
                # 短暂渲染以便观察
                env.render()
                time.sleep(0.5)
            else:
                print(f"Episode {episode + 1:2d}: 无法获取车辆信息")
        
        env.close()
        
        # 分析结果
        print("\n=== 结果分析 ===")
        
        if spawn_positions:
            x_coords = [pos[0] for pos in spawn_positions]
            y_coords = [pos[1] for pos in spawn_positions]
            headings = [np.degrees(h) for h in spawn_headings]
            
            print(f"X坐标统计:")
            print(f"  范围: {min(x_coords):.2f} 到 {max(x_coords):.2f}")
            print(f"  平均: {np.mean(x_coords):.2f}")
            print(f"  标准差: {np.std(x_coords):.2f}")
            
            print(f"Y坐标统计:")
            print(f"  范围: {min(y_coords):.2f} 到 {max(y_coords):.2f}")
            print(f"  平均: {np.mean(y_coords):.2f}")
            print(f"  标准差: {np.std(y_coords):.2f}")
            
            print(f"朝向统计:")
            print(f"  范围: {min(headings):.1f}° 到 {max(headings):.1f}°")
            print(f"  平均: {np.mean(headings):.1f}°")
            print(f"  标准差: {np.std(headings):.1f}°")
            
            # 验证约束条件
            print(f"\n约束验证:")
            
            # 检查x坐标范围
            x_in_range = all(-25 <= x <= 25 for x in x_coords)
            print(f"  X坐标在范围内(-25到25): {'✓' if x_in_range else '✗'}")
            
            # 检查y坐标固定
            y_fixed = all(abs(y) < 0.1 for y in y_coords)  # 允许小误差
            print(f"  Y坐标固定在0附近: {'✓' if y_fixed else '✗'}")
            
            # 检查位置多样性
            x_diversity = np.std(x_coords) > 5.0  # 标准差大于5米
            print(f"  X坐标有足够多样性: {'✓' if x_diversity else '✗'}")
            
            # 检查朝向随机性
            heading_diversity = np.std(headings) > 30.0  # 标准差大于30度
            print(f"  朝向有足够随机性: {'✓' if heading_diversity else '✗'}")
            
            # 总体评估
            all_good = x_in_range and y_fixed and x_diversity and heading_diversity
            print(f"\n总体评估: {'🎉 随机生成功能正常工作！' if all_good else '⚠️ 可能需要调整'}")
            
        else:
            print("❌ 无法获取足够的测试数据")
            
    except KeyboardInterrupt:
        print("用户中断测试")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

def test_boundary_safety():
    """测试边界安全性"""
    print("\n=== 边界安全性测试 ===\n")
    
    config = RandomSpawnConfig()
    
    try:
        env = CustomParkingEnv(config)
        
        print("进行50次重置，检查是否有车辆生成在危险位置...")
        
        dangerous_spawns = 0
        total_tests = 50
        
        for i in range(total_tests):
            obs, info = env.reset()
            
            if env.controlled_vehicles:
                vehicle = env.controlled_vehicles[0]
                x, y = vehicle.position
                
                # 检查是否太靠近边界
                # 停车场宽度约70米，所以边界在±35米
                too_close_to_wall = abs(x) > 30  # 距离墙壁小于5米
                
                if too_close_to_wall:
                    dangerous_spawns += 1
                    print(f"  ⚠️ 第{i+1}次: 位置({x:.2f}, {y:.2f})太靠近墙壁")
        
        env.close()
        
        safety_rate = (total_tests - dangerous_spawns) / total_tests * 100
        print(f"\n安全性统计:")
        print(f"  总测试次数: {total_tests}")
        print(f"  危险生成次数: {dangerous_spawns}")
        print(f"  安全率: {safety_rate:.1f}%")
        
        if safety_rate >= 95:
            print("  ✅ 边界安全性良好")
        elif safety_rate >= 80:
            print("  ⚠️ 边界安全性一般，建议调整")
        else:
            print("  ❌ 边界安全性差，需要修复")
            
    except Exception as e:
        print(f"边界安全性测试出错: {e}")

if __name__ == "__main__":
    test_random_spawn_positions()
    test_boundary_safety()
    
    print("\n功能总结:")
    print("1. ✅ 实现了x坐标的随机化")
    print("2. ✅ 保持y坐标固定为0")
    print("3. ✅ 添加了边界安全距离")
    print("4. ✅ 保持了朝向的随机性")
    print("5. 🎯 增加了训练的多样性和泛化能力")
