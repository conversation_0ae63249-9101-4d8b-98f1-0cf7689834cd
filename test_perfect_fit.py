#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试精确匹配停车场边界的窗口配置
验证是否能消除黑边问题
"""

import numpy as np
import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms/environments/single_agent_env'))

from custom_parking import CustomParkingEnv

class PerfectFitConfig:
    """精确匹配配置类"""
    def __init__(self):
        # 基本环境配置
        self.env_seed = 42
        self.max_episode_steps = 30
        self.render_mode = 'human'
        
        # 其他配置
        self.collision_reward = -10

def test_perfect_fit_display():
    """测试精确匹配的显示效果"""
    print("=== 精确匹配边界的窗口配置测试 ===\n")
    
    # 创建配置
    config = PerfectFitConfig()
    
    print("窗口配置计算:")
    print("- 停车场实际尺寸: 70m × 42m")
    print("- 宽高比: 70:42 ≈ 1.67:1")
    print("- 目标窗口尺寸: 840px × 504px")
    print("- 计算缩放比例: 840/70 = 12")
    print("- 理论效果: 窗口刚好包住停车场边界")
    print()
    
    # 创建环境
    env = CustomParkingEnv(config)
    
    try:
        print("正在启动环境...")
        
        # 重置环境
        obs, info = env.reset()
        print("环境已重置")
        print(f"路径点数量: {info.get('path_length', 0)}")
        
        # 渲染初始状态
        env.render()
        print("初始状态已渲染")
        
        print("\n请仔细检查窗口显示:")
        print("1. 🎯 黑边是否完全消失？")
        print("2. 🎯 停车场是否刚好填满整个窗口？")
        print("3. 🎯 上下左右边界是否都贴近窗口边缘？")
        print("4. 🎯 路径显示是否清晰可见？")
        print("5. 🎯 车辆和停车位是否显示正常？")
        print()
        
        # 运行几步来观察动态效果
        print("运行几步来观察动态效果...")
        for step in range(10):
            # 随机动作
            action = env.action_space.sample()
            
            # 执行动作
            obs, reward, terminated, truncated, info = env.step(action)
            
            # 渲染
            env.render()
            
            print(f"Step {step + 1}: reward={reward:.3f}")
            
            # 如果episode结束，跳出循环
            if terminated or truncated:
                print(f"Episode结束于第 {step + 1} 步")
                break
                
            # 短暂延迟以便观察
            time.sleep(0.4)
        
        print("\n测试完成！")
        
        # 获取用户反馈
        feedback = input("\n黑边问题是否解决？(y/n): ").lower().strip()
        
        if feedback in ['y', 'yes', '是']:
            print("🎉 太好了！精确匹配边界的方法成功解决了黑边问题！")
            print("现在窗口大小刚好包住停车场，没有多余的空间。")
        else:
            print("如果还有问题，我们可以进一步微调缩放比例或窗口尺寸。")
            
            # 询问具体问题
            issue = input("还有什么问题？(上下黑边/左右黑边/其他): ").strip()
            if "上下" in issue:
                print("建议: 减少screen_height或增加parking_height的考虑")
            elif "左右" in issue:
                print("建议: 减少screen_width或增加parking_width的考虑")
            else:
                print("建议: 可以微调scaling值来精细调整")
        
        input("\n按Enter键关闭环境...")
        
    except KeyboardInterrupt:
        print("用户中断测试")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    finally:
        env.close()
        print("环境已关闭")

def show_calculation_details():
    """显示计算细节"""
    print("=== 精确匹配计算细节 ===\n")
    
    print("1. 停车场实际尺寸分析:")
    print("   - 从ParkingEnv源码得知: width=70m, height=42m")
    print("   - 这是停车场墙壁围成的实际区域")
    print("   - 宽高比 = 70:42 ≈ 1.67:1")
    print()
    
    print("2. 窗口尺寸计算:")
    print("   - 选择基准宽度: 840px (70的12倍)")
    print("   - 对应高度: 840 × (42/70) = 504px")
    print("   - 保持了原始的宽高比")
    print()
    
    print("3. 缩放比例计算:")
    print("   - scaling = screen_width / parking_width")
    print("   - scaling = 840 / 70 = 12")
    print("   - 这意味着每米对应12像素")
    print()
    
    print("4. 理论效果:")
    print("   - 停车场的70m宽度 → 840px")
    print("   - 停车场的42m高度 → 504px")
    print("   - 窗口刚好包住停车场边界")
    print("   - 应该没有多余的黑边")
    print()
    
    print("5. 对比原始配置:")
    print("   - 原始: 600×300, scaling=7")
    print("   - 新配置: 840×504, scaling=12")
    print("   - 更精确的尺寸匹配")

if __name__ == "__main__":
    show_calculation_details()
    print("\n" + "="*50 + "\n")
    
    # 询问是否运行测试
    response = input("是否运行精确匹配测试？(y/n): ").lower().strip()
    
    if response in ['y', 'yes', '是']:
        test_perfect_fit_display()
    else:
        print("测试已取消")
        print("\n配置已更新，下次运行环境时将使用新的精确匹配设置。")
