#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动截图功能
演示如何在每次生成地图和路径时自动保存图片
"""

import numpy as np
import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms/environments/single_agent_env'))

from custom_parking import CustomParkingEnv

class ScreenshotConfig:
    """截图配置类"""
    def __init__(self):
        # 基本环境配置
        self.env_seed = 42
        self.max_episode_steps = 20
        self.render_mode = 'human'
        
        # 截图相关配置
        self.auto_save_screenshots = True  # 启用自动截图
        self.screenshot_dir = 'parking_screenshots'  # 截图保存目录
        
        # 其他配置
        self.collision_reward = -10

def test_auto_screenshot():
    """测试自动截图功能"""
    print("=== 自动截图功能测试 ===\n")
    
    # 创建配置
    config = ScreenshotConfig()
    
    print(f"截图将保存到目录: {config.screenshot_dir}")
    print(f"自动截图已启用: {config.auto_save_screenshots}")
    print()
    
    # 创建环境
    env = CustomParkingEnv(config)
    
    try:
        # 运行几个episode来测试截图功能
        for episode in range(3):
            print(f"开始Episode {episode + 1}")
            
            # 重置环境（会保存初始状态截图）
            obs, info = env.reset()
            print(f"  环境已重置，路径规划完成")
            print(f"  路径点数量: {info.get('path_length', 0)}")
            
            # 运行几步
            for step in range(10):
                # 随机动作（实际应用中这里会是智能体的动作）
                action = env.action_space.sample()
                
                # 执行动作
                obs, reward, terminated, truncated, info = env.step(action)
                
                print(f"  Step {step + 1}: reward={reward:.3f}, terminated={terminated}, truncated={truncated}")
                
                # 如果episode结束，跳出循环
                if terminated or truncated:
                    print(f"  Episode {episode + 1} 结束于第 {step + 1} 步")
                    break
                
                # 短暂延迟以便观察
                time.sleep(0.1)
            
            print(f"Episode {episode + 1} 完成\n")
            
    except KeyboardInterrupt:
        print("用户中断测试")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    finally:
        env.close()
        print("环境已关闭")

def demonstrate_screenshot_features():
    """演示截图功能的特性"""
    print("=== 截图功能特性演示 ===\n")
    
    print("1. 自动截图时机:")
    print("   - 环境重置后（初始状态）")
    print("   - 第一步执行后")
    print("   - 每5步保存一次")
    print("   - Episode终止时")
    print("   - Episode被截断时")
    print()
    
    print("2. 截图文件命名规则:")
    print("   episode_XXXX_step_XXXX_YYYYMMDD_HHMMSS_suffix.png")
    print("   - episode_XXXX: Episode编号（4位数字）")
    print("   - step_XXXX: 步数编号（4位数字）")
    print("   - YYYYMMDD_HHMMSS: 时间戳")
    print("   - suffix: 特殊后缀（如_initial, _terminated等）")
    print()
    
    print("3. 截图内容包含:")
    print("   - 停车场地图")
    print("   - 车辆当前位置和朝向")
    print("   - 规划的路径（红色线条）")
    print("   - 起点（绿色圆圈）")
    print("   - 终点（蓝色方块）")
    print("   - 其他车辆（如果有）")
    print()

def check_screenshot_directory():
    """检查截图目录"""
    screenshot_dir = 'parking_screenshots'
    
    if os.path.exists(screenshot_dir):
        files = [f for f in os.listdir(screenshot_dir) if f.endswith('.png')]
        print(f"截图目录 '{screenshot_dir}' 中有 {len(files)} 个PNG文件")
        
        if files:
            print("最近的几个截图文件:")
            # 按修改时间排序，显示最新的5个
            files_with_time = [(f, os.path.getmtime(os.path.join(screenshot_dir, f))) for f in files]
            files_with_time.sort(key=lambda x: x[1], reverse=True)
            
            for i, (filename, _) in enumerate(files_with_time[:5]):
                print(f"  {i+1}. {filename}")
    else:
        print(f"截图目录 '{screenshot_dir}' 不存在")

if __name__ == "__main__":
    # 演示功能特性
    demonstrate_screenshot_features()
    
    # 检查现有截图
    check_screenshot_directory()
    
    # 询问是否运行测试
    response = input("是否运行自动截图测试？(y/n): ").lower().strip()
    
    if response in ['y', 'yes', '是']:
        test_auto_screenshot()
        print("\n测试完成！")
        
        # 再次检查截图目录
        print("\n测试后的截图目录状态:")
        check_screenshot_directory()
        
        print(f"\n您可以在 'parking_screenshots' 目录中查看保存的截图。")
        print("这些截图记录了车辆在停车过程中的每个关键时刻。")
    else:
        print("测试已取消")
