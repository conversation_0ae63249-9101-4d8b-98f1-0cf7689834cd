#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试使用父类配置的显示效果
验证黑边问题是否通过使用父类配置得到解决
"""

import numpy as np
import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms/environments/single_agent_env'))

from custom_parking import CustomParkingEnv

class ParentConfigTest:
    """使用父类配置的测试类"""
    def __init__(self):
        # 基本环境配置
        self.env_seed = 42
        self.max_episode_steps = 30
        self.render_mode = 'human'
        
        # 其他配置
        self.collision_reward = -10

def test_parent_config_display():
    """测试使用父类配置的显示效果"""
    print("=== 使用父类配置的显示测试 ===\n")
    
    # 创建配置
    config = ParentConfigTest()
    
    print("配置说明:")
    print("- 使用父类ParkingEnv的默认窗口配置")
    print("- 只添加路径绘制功能")
    print("- 不覆盖窗口尺寸和缩放设置")
    print("- 保持原始的渲染优化")
    print()
    
    # 创建环境
    env = CustomParkingEnv(config)
    
    try:
        print("正在启动环境...")
        
        # 重置环境
        obs, info = env.reset()
        print("环境已重置")
        print(f"路径点数量: {info.get('path_length', 0)}")
        
        # 渲染初始状态
        env.render()
        print("初始状态已渲染")
        
        print("\n请检查窗口显示:")
        print("1. 黑边是否减少或消失？")
        print("2. 窗口大小是否更合适？")
        print("3. 停车场内容是否更好地填充窗口？")
        print("4. 路径显示是否正常？")
        print("5. 整体视觉效果是否改善？")
        print()
        
        # 运行几步来观察效果
        print("运行几步来观察动态效果...")
        for step in range(15):
            # 随机动作
            action = env.action_space.sample()
            
            # 执行动作
            obs, reward, terminated, truncated, info = env.step(action)
            
            # 渲染
            env.render()
            
            print(f"Step {step + 1}: reward={reward:.3f}")
            
            # 如果episode结束，跳出循环
            if terminated or truncated:
                print(f"Episode结束于第 {step + 1} 步")
                break
                
            # 短暂延迟以便观察
            time.sleep(0.3)
        
        print("\n测试完成！")
        
        # 等待用户反馈
        feedback = input("\n显示效果是否改善？(y/n): ").lower().strip()
        
        if feedback in ['y', 'yes', '是']:
            print("太好了！使用父类配置确实解决了黑边问题。")
        else:
            print("如果问题仍然存在，可能需要检查系统显示设置或尝试其他方法。")
        
        input("\n按Enter键关闭环境...")
        
    except KeyboardInterrupt:
        print("用户中断测试")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    finally:
        env.close()
        print("环境已关闭")

def compare_configs():
    """比较自定义配置和父类配置的差异"""
    print("=== 配置对比 ===\n")
    
    print("自定义配置的问题:")
    print("- 可能覆盖了父类优化的窗口设置")
    print("- 缩放和尺寸设置可能不匹配")
    print("- 摄像头位置设置可能导致显示问题")
    print("- 过度自定义可能破坏原有的显示平衡")
    print()
    
    print("使用父类配置的优势:")
    print("- 父类已经过充分测试和优化")
    print("- 窗口尺寸和缩放比例经过调优")
    print("- 避免了不必要的显示问题")
    print("- 保持了原有的稳定性")
    print("- 只添加我们需要的路径绘制功能")
    print()
    
    print("当前策略:")
    print("- 继承父类的所有显示配置")
    print("- 只在渲染时添加路径绘制")
    print("- 不修改窗口、缩放等核心显示参数")
    print("- 保持最小化的自定义修改")

if __name__ == "__main__":
    compare_configs()
    print("\n" + "="*50 + "\n")
    
    # 询问是否运行测试
    response = input("是否运行父类配置显示测试？(y/n): ").lower().strip()
    
    if response in ['y', 'yes', '是']:
        test_parent_config_display()
    else:
        print("测试已取消")
        print("\n现在的配置应该能够解决黑边问题，因为我们使用了父类经过优化的设置。")
