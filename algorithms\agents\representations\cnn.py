# ================================================================================================
# CNN（卷积神经网络）表征网络实现
#
# 这个文件实现了基于卷积神经网络的表征网络，用于处理图像类型的观察输入：
# 1. Basic_CNN - 标准的卷积神经网络，用于图像特征提取
# 2. AC_CNN_MLP - 结合CNN和MLP的混合网络
#
# CNN适用场景：
# - 图像输入（如Atari游戏画面、摄像头图像等）
# - 需要空间特征提取的任务
# - 具有空间结构的观察数据
# ================================================================================================

import numpy as np                                    # 数值计算库
from algorithms.utils import Sequence, Optional, Union, Callable  # 类型注解
from algorithms.agents import Module, Tensor         # 神经网络模块基类
import torch                                         # PyTorch深度学习框架
from torch import nn                                 # PyTorch神经网络模块
from algorithms.agents.utils import cnn_block, mlp_block, ModuleType  # CNN和MLP构建工具


class Basic_CNN(Module):
    """
    基础卷积神经网络表征网络

    这是专门用于处理图像输入的CNN网络，通过多个卷积层提取空间特征。
    适用于Atari游戏、机器人视觉、自动驾驶等需要处理图像的强化学习任务。

    🏗️ **网络结构**：
    图像输入 → 卷积层1 → 激活 → 卷积层2 → 激活 → ... → 全局池化 → 展平 → 特征向量

    🎯 **使用场景**：
    - Atari游戏：处理游戏画面 (84x84x4)
    - 机器人视觉：处理摄像头图像
    - 自动驾驶：处理道路图像
    - 任何具有空间结构的观察

    📊 **典型配置**：
    - kernels: [8, 4, 3] (卷积核大小)
    - strides: [4, 2, 1] (步长)
    - filters: [32, 64, 64] (通道数)

    Args:
        input_shape (Sequence[int]): 输入图像形状 [H, W, C]
        kernels (Sequence[int]): 各卷积层的卷积核大小
        strides (Sequence[int]): 各卷积层的步长
        filters (Sequence[int]): 各卷积层的输出通道数
        normalize (Optional[ModuleType]): 归一化层类型
        initialize (Optional[Callable]): 权重初始化方法
        activation (Optional[ModuleType]): 激活函数类型
        device (Optional[Union[str, int, torch.device]]): 计算设备
    """

    def __init__(self,
                 input_shape: Sequence[int],
                 kernels: Sequence[int],
                 strides: Sequence[int],
                 filters: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None,
                 **kwargs):
        """
        初始化CNN表征网络

        Args:
            input_shape: 输入图像形状 [高度, 宽度, 通道数]
            kernels: 卷积核大小列表，如 [8, 4, 3]
            strides: 步长列表，如 [4, 2, 1]
            filters: 输出通道数列表，如 [32, 64, 64]
        """
        super(Basic_CNN, self).__init__()

        # 转换输入形状：从 [H, W, C] 到 [C, H, W] (PyTorch格式)
        self.input_shape = (input_shape[2], input_shape[0], input_shape[1])

        # 保存网络配置参数
        self.kernels = kernels
        self.strides = strides
        self.filters = filters
        self.normalize = normalize
        self.initialize = initialize
        self.activation = activation
        self.device = device

        # 设置输出形状（最后一个卷积层的通道数）
        self.output_shapes = {'state': (filters[-1],)}

        # 构建网络结构
        self.model = self._create_network()

    def _create_network(self):
        """
        创建CNN网络结构

        构建多个卷积块，每个块包含：卷积层 + 归一化层（可选）+ 激活函数
        最后添加全局池化和展平层，将2D特征图转换为1D特征向量。

        Returns:
            nn.Sequential: 完整的CNN网络
        """
        layers = []
        input_shape = self.input_shape

        # 为每个卷积层创建CNN块
        for k, s, f in zip(self.kernels, self.strides, self.filters):
            # 使用cnn_block工具函数创建：卷积层 + 归一化 + 激活函数
            cnn, input_shape = cnn_block(
                input_shape,         # 输入形状 [C, H, W]
                f,                   # 输出通道数
                k,                   # 卷积核大小
                s,                   # 步长
                self.normalize,      # 归一化层
                self.activation,     # 激活函数
                self.initialize,     # 初始化方法
                self.device          # 计算设备
            )
            layers.extend(cnn)  # 添加到层列表

        # 添加全局自适应最大池化：将任意大小的特征图池化为1x1
        layers.append(nn.AdaptiveMaxPool2d((1, 1)))

        # 添加展平层：将2D特征图展平为1D向量
        layers.append(nn.Flatten())

        # 将所有层组合成顺序网络
        return nn.Sequential(*layers)

    def forward(self, observations: np.ndarray):
        """
        前向传播：将图像观察转换为特征表示

        Args:
            observations (np.ndarray): 输入图像，形状 [batch_size, H, W, C]

        Returns:
            dict: 包含'state'键的字典，值为提取的特征张量

        处理流程：
            1. 像素值归一化：[0, 255] → [0, 1]
            2. 维度转换：[B, H, W, C] → [B, C, H, W]
            3. CNN特征提取：图像 → 特征向量
        """
        # 将像素值从[0, 255]归一化到[0, 1]
        observations = observations / 255.0

        # 转换为PyTorch张量并调整维度顺序
        # 从 [batch, height, width, channels] 到 [batch, channels, height, width]
        tensor_observation = torch.as_tensor(observations, dtype=torch.float32,
                                             device=self.device).permute((0, 3, 1, 2))

        # 通过CNN网络进行特征提取
        features = self.model(tensor_observation)

        return {'state': features}


class AC_CNN_Atari(Module):
    def __init__(self,
                 input_shape: Sequence[int],
                 kernels: Sequence[int],
                 strides: Sequence[int],
                 filters: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None,
                 fc_hidden_sizes: Sequence[int] = (),
                 **kwargs):
        super(AC_CNN_Atari, self).__init__()
        self.input_shape = (input_shape[2], input_shape[0], input_shape[1])  # Channels x Height x Width
        self.kernels = kernels
        self.strides = strides
        self.filters = filters
        self.normalize = normalize
        self.initialize = initialize
        self.activation = activation
        self.device = device
        self.fc_hidden_sizes = fc_hidden_sizes
        self.output_shapes = {'state': (fc_hidden_sizes[-1],)}
        self.model = self._create_network()

    def _init_layer(self, layer, gain=np.sqrt(2), bias=0.0):
        nn.init.orthogonal_(layer.weight, gain=gain)
        nn.init.constant_(layer.bias, bias)
        return layer

    def _create_network(self):
        layers = []
        input_shape = self.input_shape
        for k, s, f in zip(self.kernels, self.strides, self.filters):
            cnn, input_shape = cnn_block(input_shape, f, k, s, None, self.activation, None, self.device)
            cnn[0] = self._init_layer(cnn[0])
            layers.extend(cnn)
        layers.append(nn.Flatten())
        input_shape = (np.prod(input_shape, dtype=np.int32), )
        for h in self.fc_hidden_sizes:
            mlp, input_shape = mlp_block(input_shape[0], h, None, self.activation, None, self.device)
            mlp[0] = self._init_layer(mlp[0])
            layers.extend(mlp)
        return nn.Sequential(*layers)

    def forward(self, observations: np.ndarray):
        observations = observations / 255.0
        tensor_observation = torch.as_tensor(observations, dtype=torch.float32,
                                             device=self.device).permute((0, 3, 1, 2))
        return {'state': self.model(tensor_observation)}
