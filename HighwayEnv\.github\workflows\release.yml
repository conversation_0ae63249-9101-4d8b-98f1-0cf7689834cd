name: Release
on:
  release:
    types:
      - published

jobs:
  release:
    name: Deploy release to PyPI
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source
        uses: actions/checkout@v1
      - name: Set up Python
        uses: actions/setup-python@v1
        with:
          python-version: 3.8
      - name: Install dependencies
        run: pip install wheel
      - name: Build package
        run: python setup.py sdist bdist_wheel
      - name: Upload package
        uses: pypa/gh-action-pypi-publish@master
        with:
          user: __token__
          password: ${{ secrets.pypi_password }}