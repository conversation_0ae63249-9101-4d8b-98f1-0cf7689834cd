name: Build main branch documentation website
on:
  push:
    branches: [master]
permissions:
  contents: write
env:
  OFFSCREEN_RENDERING: 1
jobs:
  docs:
    name: Generate Website
    runs-on: ubuntu-latest
    env:
      SPHINX_GITHUB_CHANGELOG_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v3

      - uses: actions/setup-python@v4
        with:
            python-version: '3.9'

      - name: Install dependencies
        run: pip install -r docs/requirements.txt

      - name: Register Envs
        run: pip install -e .

      - name: Build
        run: sphinx-build -b dirhtml -v docs _build

      - name: Move 404
        run: mv _build/404/index.html _build/404.html

      - name: Update 404 links
        run: python docs/scripts/move_404.py _build/404.html

      - name: Remove .doctrees
        run: rm -r _build/.doctrees

      - name: Upload to GitHub Pages
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: _build
          target-folder: main
          clean: false
