dl_toolbox: "torch"  # 深度学习框架: "torch", "mindspore", "tensorlayer"
project_name: "HRL_Highway_Project"
logger: "tensorboard"  # 日志工具: tensorboard, wandb
wandb_user_name: "your_user_name"
render: False
render_mode: 'rgb_array'  # 渲染模式: 'human', 'rgb_array'
fps: 50  # 渲染视频的帧率
test_mode: False
device: "cuda:0"
distributed_training: False  # 是否使用多GPU分布式训练
master_port: '12355'  # 分布式训练时的主端口

agent: "MAPPO"  # 多智能体PPO算法
env_name: "highway_ma"  # 多智能体highway环境
env_id: "highway-v0"  # 具体环境ID
env_seed: 1  # 环境随机种子
continuous_action: False  # highway环境通常使用离散动作
vectorize: "DummyVecMultiAgentEnv"  # 多智能体环境向量化方式
representation: "Basic_MLP"  # 表征网络类型
policy: "Categorical_MAAC"  # 多智能体分类策略
learner: "MAPPO_Learner"  # 学习器类型
runner: "MARL"  # 多智能体运行器类型

representation_hidden_size: [256,]  # 表征网络隐藏层大小
actor_hidden_size: [256,]  # 策略网络隐藏层大小
critic_hidden_size: [256,]  # 价值网络隐藏层大小
activation: "leaky_relu"  # 激活函数
use_parameter_sharing: False  # 是否使用参数共享
use_actions_mask: False  # 是否使用动作掩码

seed: 79811  # 随机种子
parallels: 8  # 并行环境数量
running_steps: 500000  # 总训练步数
buffer_size: 128  # 缓冲区大小
n_epochs: 10  # 每批数据的训练轮数
n_minibatch: 4  # mini-batch数量
learning_rate: 0.0003  # 学习率

use_grad_clip: True  # 是否使用梯度裁剪
clip_type: 1  # MindSpore的梯度裁剪类型
grad_clip_norm: 0.5  # 梯度裁剪范数

vf_coef: 0.25  # 价值函数损失系数
ent_coef: 0.01  # 熵正则化系数
target_kl: 0.25  # 目标KL散度
clip_range: 0.2  # PPO裁剪范围
gamma: 0.99  # 折扣因子
use_gae: True  # 是否使用GAE
gae_lambda: 0.95  # GAE lambda参数
use_advnorm: True  # 是否使用优势归一化

use_value_clip: True  # 是否使用价值裁剪
value_clip_range: 0.2  # 价值裁剪范围
use_value_norm: True  # 是否使用价值归一化
use_huber_loss: True  # 是否使用Huber损失
huber_delta: 10.0  # Huber损失的delta参数

use_obsnorm: True  # 是否使用观察归一化
use_rewnorm: True  # 是否使用奖励归一化
obsnorm_range: 5  # 观察归一化范围
rewnorm_range: 5  # 奖励归一化范围

test_steps: 10000  # 测试步数
eval_interval: 5000  # 评估间隔
test_episode: 5  # 测试回合数
log_dir: "./logs/mappo/"  # 日志目录
model_dir: "./models/mappo/"  # 模型保存目录
