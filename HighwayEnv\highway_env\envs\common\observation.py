# 导入必要的库
from collections import OrderedDict  # 有序字典，用于保持观察空间的顺序
from itertools import product      # 用于生成笛卡尔积
from typing import TYPE_CHECKING, Dict, List, Optional, Tuple  # 类型提示

import numpy as np
import pandas as pd
from gymnasium import spaces  # OpenAI Gym的空间定义

# 导入自定义模块
from highway_env import utils
from highway_env.envs.common.finite_mdp import compute_ttc_grid
from highway_env.envs.common.graphics import EnvViewer
from highway_env.road.lane import AbstractLane
from highway_env.utils import Vector
from highway_env.vehicle.kinematics import Vehicle

if TYPE_CHECKING:
    from highway_env.envs.common.abstract import AbstractEnv


class ObservationType(object):
    """
    观察类型的基类。
    定义了所有观察类型必须实现的基本接口。
    """

    def __init__(self, env: "AbstractEnv", **kwargs) -> None:
        """
        初始化观察类型。
        
        参数:
            env: 环境实例
            kwargs: 额外的关键字参数
        """
        self.env = env                    # 保存环境引用
        self.__observer_vehicle = None    # 观察者车辆（默认为None）

    def space(self) -> spaces.Space:
        """
        获取观察空间的定义。
        
        返回:
            spaces.Space: 定义了观察值的有效范围和形状的空间对象
        """
        raise NotImplementedError

    def observe(self):
        """
        获取环境当前状态的观察值。
        
        返回:
            观察值，具体类型取决于子类实现
        """
        raise NotImplementedError

    @property
    def observer_vehicle(self):
        """
        获取观察者车辆。
        如果未设置，默认使用环境中第一个被控制的车辆。

        返回:
            Vehicle: 观察者车辆实例
        """
        return self.__observer_vehicle or self.env.vehicle

    @observer_vehicle.setter
    def observer_vehicle(self, vehicle):
        """
        设置观察者车辆。
        
        参数:
            vehicle: 要设置为观察者的车辆实例
        """
        self.__observer_vehicle = vehicle


class GrayscaleObservation(ObservationType):
    """
    灰度图像观察类型。

    直接收集模拟器渲染的图像作为观察值。
    类似于Nature DQN中的方法，将连续的几帧图像堆叠在一起。
    观察值的形状为 C x W x H（通道数 x 宽度 x 高度）。

    配置示例：
        observation": {
            "type": "GrayscaleObservation",
        "observation_shape": (84, 84)     # 图像尺寸
        "stack_size": 4,                  # 堆叠帧数
        "weights": [0.2989, 0.5870, 0.1140],  # RGB转灰度的权重
        }
    """

    def __init__(
        self,
        env: "AbstractEnv",
        observation_shape: Tuple[int, int],   # 观察图像的形状
        stack_size: int,                      # 堆叠的帧数
        weights: List[float],                 # RGB转灰度的权重
        scaling: Optional[float] = None,      # 缩放因子
        centering_position: Optional[List[float]] = None,  # 中心位置
        **kwargs
    ) -> None:
        """
        初始化灰度图像观察器。
        
        参数:
            env: 环境实例
            observation_shape: 观察图像的尺寸 (width, height)
            stack_size: 要堆叠的连续帧数
            weights: RGB转灰度时的权重
            scaling: 图像缩放因子
            centering_position: 视角中心位置
            kwargs: 其他参数
        """
        super().__init__(env)
        self.observation_shape = observation_shape
        self.shape = (stack_size,) + self.observation_shape  # 最终观察值的形状
        self.weights = weights                               # RGB权重
        self.obs = np.zeros(self.shape, dtype=np.uint8)     # 初始化观察数组

        # 配置观察者的渲染设置（可能与env.render()不同，通常更小）
        viewer_config = env.config.copy()
        viewer_config.update(
            {
                "offscreen_rendering": True,                # 离屏渲染
                "screen_width": self.observation_shape[0],  # 屏幕宽度
                "screen_height": self.observation_shape[1], # 屏幕高度
                "scaling": scaling or viewer_config["scaling"],  # 缩放
                "centering_position": centering_position    # 中心位置
                or viewer_config["centering_position"],
            }
        )
        self.viewer = EnvViewer(env, config=viewer_config)  # 创建观察者的渲染器

    def space(self) -> spaces.Space:
        """
        定义观察空间。
        
        返回:
            spaces.Box: 定义了图像数据的取值范围[0, 255]和形状的空间
        """
        return spaces.Box(shape=self.shape, low=0, high=255, dtype=np.uint8)

    def observe(self) -> np.ndarray:
        """
        获取当前的灰度图像观察值。
        
        将新的观察帧添加到堆叠序列中，保持固定帧数。
        
        返回:
            np.ndarray: 堆叠的灰度图像序列
        """
        new_obs = self._render_to_grayscale()  # 获取新的灰度图像
        self.obs = np.roll(self.obs, -1, axis=0)  # 将历史观察向前滚动一帧
        self.obs[-1, :, :] = new_obs  # 在最后位置添加新的观察
        return self.obs

    def _render_to_grayscale(self) -> np.ndarray:
        """
        将当前场景渲染为灰度图像。
        
        1. 设置观察者视角
        2. 渲染场景
        3. 将RGB图像转换为灰度图像
        
        返回:
            np.ndarray: 灰度图像数据
        """
        self.viewer.observer_vehicle = self.observer_vehicle  # 设置视角
        self.viewer.display()  # 渲染场景
        raw_rgb = self.viewer.get_image()  # 获取RGB图像 (H x W x C)
        raw_rgb = np.moveaxis(raw_rgb, 0, 1)  # 调整轴顺序
        # 使用权重将RGB转换为灰度，并裁剪到[0, 255]范围
        return np.dot(raw_rgb[..., :3], self.weights).clip(0, 255).astype(np.uint8)


class TimeToCollisionObservation(ObservationType):
    """
    碰撞时间观察类型。
    
    计算当前车辆与周围车辆可能发生碰撞的时间，
    形成一个三维网格(速度, 车道, 时间)的观察空间。
    """

    def __init__(self, env: "AbstractEnv", horizon: int = 10, **kwargs: dict) -> None:
        """
        初始化碰撞时间观察器。
        
        参数:
            env: 环境实例
            horizon: 预测时间范围（步数）
            kwargs: 其他参数
        """
        super().__init__(env)
        self.horizon = horizon  # 预测时间范围

    def space(self) -> spaces.Space:
        """
        定义观察空间。
        
        返回:
            spaces.Box: 定义了碰撞时间网格的取值范围[0, 1]和形状的空间
        """
        try:
            return spaces.Box(
                shape=self.observe().shape, low=0, high=1, dtype=np.float32
            )
        except AttributeError:
            return spaces.Space()

    def observe(self) -> np.ndarray:
        """
        计算碰撞时间网格。
        
        如果道路为空，返回全零数组；
        否则计算与周围车辆的碰撞时间网格。
        
        返回:
            np.ndarray: 碰撞时间网格数据
        """
        if not self.env.road:  # 如果道路为空
            return np.zeros(
                (3, 3, int(self.horizon * self.env.config["policy_frequency"]))
            )
        
        # 计算碰撞时间网格
        grid = compute_ttc_grid(
            self.env,
            vehicle=self.observer_vehicle,
            time_quantization=1 / self.env.config["policy_frequency"],
            horizon=self.horizon,
        )
        
        # 添加边界填充
        padding = np.ones(np.shape(grid))
        padded_grid = np.concatenate([padding, grid, padding], axis=1)
        
        # 提取观察车道
        obs_lanes = 3
        l0 = grid.shape[1] + self.observer_vehicle.lane_index[2] - obs_lanes // 2
        lf = grid.shape[1] + self.observer_vehicle.lane_index[2] + obs_lanes // 2
        clamped_grid = padded_grid[:, l0 : lf + 1, :]
        
        # 重复边界车道
        repeats = np.ones(clamped_grid.shape[0])
        repeats[np.array([0, -1])] += clamped_grid.shape[0]
        padded_grid = np.repeat(clamped_grid, repeats.astype(int), axis=0)
        
        # 提取观察速度范围
        obs_speeds = 3
        v0 = grid.shape[0] + self.observer_vehicle.speed_index - obs_speeds // 2
        vf = grid.shape[0] + self.observer_vehicle.speed_index + obs_speeds // 2
        clamped_grid = padded_grid[v0 : vf + 1, :, :]
        
        return clamped_grid.astype(np.float32)


class KinematicObservation(ObservationType):
    """
    运动学观察类型。
    
    观察周围车辆的运动学状态，包括：
    - 位置 (x, y)
    - 速度 (vx, vy)
    - 是否存在
    """

    # 默认观察特征
    FEATURES: List[str] = ["presence", "x", "y", "vx", "vy"]

    def __init__(
        self,
        env: "AbstractEnv",
        features: List[str] = None,         # 要观察的特征列表
        vehicles_count: int = 5,            # 观察的车辆数量
        features_range: Dict[str, List[float]] = None,  # 特征值范围
        absolute: bool = False,             # 是否使用绝对坐标
        order: str = "sorted",              # 车辆排序方式
        normalize: bool = True,             # 是否归一化
        clip: bool = True,                  # 是否裁剪值
        see_behind: bool = False,           # 是否观察后方车辆
        observe_intentions: bool = False,    # 是否观察其他车辆的意图
        include_obstacles: bool = True,      # 是否包含障碍物
        **kwargs: dict
    ) -> None:
        """
        初始化运动学观察器。
        
        参数:
            env: 环境实例
            features: 要观察的特征列表，默认为FEATURES
            vehicles_count: 最多观察的车辆数量
            features_range: 特征值的范围字典
            absolute: 是否使用绝对坐标而不是相对坐标
            order: 车辆排序方式 ('sorted'或'shuffled')
            normalize: 是否将观察值归一化到[-1,1]
            clip: 是否裁剪超出范围的值
            see_behind: 是否观察车辆后方
            observe_intentions: 是否观察其他车辆的目标
            include_obstacles: 是否包含障碍物在观察中
        """
        super().__init__(env)
        self.features = features or self.FEATURES
        self.vehicles_count = vehicles_count
        self.features_range = features_range
        self.absolute = absolute
        self.order = order
        self.normalize = normalize
        self.clip = clip
        self.see_behind = see_behind
        self.observe_intentions = observe_intentions
        self.include_obstacles = include_obstacles

    def space(self) -> spaces.Space:
        """
        定义观察空间。
        
        返回一个Box空间，形状为 (车辆数量, 特征数量)。
        每个车辆都有一组特征值。
        
        返回:
            spaces.Box: 观察空间定义
        """
        return spaces.Box(
            shape=(self.vehicles_count, len(self.features)),
            low=-np.inf,
            high=np.inf,
            dtype=np.float32,
        )

    def normalize_obs(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        归一化观察值。
        
        目前假设道路沿x轴方向笔直。
        将各个特征值映射到[-1,1]范围内。
        
        参数:
            df: 包含观察值的DataFrame
        返回:
            归一化后的DataFrame
        """
        if not self.features_range:
            # 如果未指定范围，计算默认范围
            side_lanes = self.env.road.network.all_side_lanes(
                self.observer_vehicle.lane_index
            )
            self.features_range = {
                # x方向范围：最大速度的±5倍
                "x": [-5.0 * Vehicle.MAX_SPEED, 5.0 * Vehicle.MAX_SPEED],
                # y方向范围：车道宽度的倍数
                "y": [
                    -AbstractLane.DEFAULT_WIDTH * len(side_lanes),
                    AbstractLane.DEFAULT_WIDTH * len(side_lanes),
                ],
                # 速度范围：最大速度的±2倍
                "vx": [-2 * Vehicle.MAX_SPEED, 2 * Vehicle.MAX_SPEED],
                "vy": [-2 * Vehicle.MAX_SPEED, 2 * Vehicle.MAX_SPEED],
            }
        
        # 对每个特征进行归一化
        for feature, f_range in self.features_range.items():
            if feature in df:
                # 线性映射到[-1,1]范围
                df[feature] = utils.lmap(df[feature], [f_range[0], f_range[1]], [-1, 1])
                # 如果需要，裁剪到[-1,1]范围
                if self.clip:
                    df[feature] = np.clip(df[feature], -1, 1)
        return df

    def observe(self) -> np.ndarray:
        """
        获取周围车辆的运动学观察值。
        
        步骤：
        1. 如果道路为空，返回零矩阵
        2. 添加自身车辆信息
        3. 添加周围车辆信息
        4. 归一化和裁剪
        5. 填充缺失值
        6. 可选的随机打乱
        
        返回:
            np.ndarray: 观察值数组，形状为(车辆数, 特征数)
        """
        if not self.env.road:
            return np.zeros(self.space().shape)

        # 添加自身车辆信息
        df = pd.DataFrame.from_records([self.observer_vehicle.to_dict()])
        
        # 添加周围车辆信息
        close_vehicles = self.env.road.close_objects_to(
            self.observer_vehicle,
            self.env.PERCEPTION_DISTANCE,        # 感知距离内
            count=self.vehicles_count - 1,       # 指定数量
            see_behind=self.see_behind,          # 是否看后方
            sort=self.order == "sorted",         # 是否排序
            vehicles_only=not self.include_obstacles,  # 是否只要车辆
        )
        
        if close_vehicles:
            # 获取参考系（绝对或相对）
            origin = self.observer_vehicle if not self.absolute else None
            # 将周围车辆信息转换为DataFrame
            vehicles_df = pd.DataFrame.from_records(
                [
                    v.to_dict(origin, observe_intentions=self.observe_intentions)
                    for v in close_vehicles[-self.vehicles_count + 1 :]
                ]
            )
            df = pd.concat([df, vehicles_df], ignore_index=True)

        # 只保留需要的特征
        df = df[self.features]

        # 归一化和裁剪
        if self.normalize:
            df = self.normalize_obs(df)
            
        # 如果车辆数量不足，用零填充
        if df.shape[0] < self.vehicles_count:
            rows = np.zeros((self.vehicles_count - df.shape[0], len(self.features)))
            df = pd.concat(
                [df, pd.DataFrame(data=rows, columns=self.features)], ignore_index=True
            )
            
        # 确保特征顺序正确
        df = df[self.features]
        obs = df.values.copy()
        
        # 如果需要随机打乱（除了自身车辆）
        if self.order == "shuffled":
            self.env.np_random.shuffle(obs[1:])
            
        # 返回正确类型的数组
        return obs.astype(self.space().dtype)


class OccupancyGridObservation(ObservationType):

    """Observe an occupancy grid of nearby vehicles."""

    FEATURES: List[str] = ["presence", "vx", "vy", "on_road"]
    GRID_SIZE: List[List[float]] = [[-5.5 * 5, 5.5 * 5], [-5.5 * 5, 5.5 * 5]]
    GRID_STEP: List[int] = [5, 5]

    def __init__(
        self,
        env: "AbstractEnv",
        features: Optional[List[str]] = None,
        grid_size: Optional[Tuple[Tuple[float, float], Tuple[float, float]]] = None,
        grid_step: Optional[Tuple[float, float]] = None,
        features_range: Dict[str, List[float]] = None,
        absolute: bool = False,
        align_to_vehicle_axes: bool = False,
        clip: bool = True,
        as_image: bool = False,
        **kwargs: dict
    ) -> None:
        """
        :param env: The environment to observe
        :param features: Names of features used in the observation
        :param grid_size: real world size of the grid [[min_x, max_x], [min_y, max_y]]
        :param grid_step: steps between two cells of the grid [step_x, step_y]
        :param features_range: a dict mapping a feature name to [min, max] values
        :param absolute: use absolute or relative coordinates
        :param align_to_vehicle_axes: if True, the grid axes are aligned with vehicle axes. Else, they are aligned
               with world axes.
        :param clip: clip the observation in [-1, 1]
        """
        super().__init__(env)
        self.features = features if features is not None else self.FEATURES
        self.grid_size = (
            np.array(grid_size) if grid_size is not None else np.array(self.GRID_SIZE)
        )
        self.grid_step = (
            np.array(grid_step) if grid_step is not None else np.array(self.GRID_STEP)
        )
        grid_shape = np.asarray(
            np.floor((self.grid_size[:, 1] - self.grid_size[:, 0]) / self.grid_step),
            dtype=np.uint8,
        )
        self.grid = np.zeros((len(self.features), *grid_shape))
        self.features_range = features_range
        self.absolute = absolute
        self.align_to_vehicle_axes = align_to_vehicle_axes
        self.clip = clip
        self.as_image = as_image

    def space(self) -> spaces.Space:
        if self.as_image:
            return spaces.Box(shape=self.grid.shape, low=0, high=255, dtype=np.uint8)
        else:
            return spaces.Box(
                shape=self.grid.shape, low=-np.inf, high=np.inf, dtype=np.float32
            )

    def normalize(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Normalize the observation values.

        For now, assume that the road is straight along the x axis.
        :param Dataframe df: observation data
        """
        if not self.features_range:
            self.features_range = {
                "vx": [-2 * Vehicle.MAX_SPEED, 2 * Vehicle.MAX_SPEED],
                "vy": [-2 * Vehicle.MAX_SPEED, 2 * Vehicle.MAX_SPEED],
            }
        for feature, f_range in self.features_range.items():
            if feature in df:
                df[feature] = utils.lmap(df[feature], [f_range[0], f_range[1]], [-1, 1])
        return df

    def observe(self) -> np.ndarray:
        if not self.env.road:
            return np.zeros(self.space().shape)

        if self.absolute:
            raise NotImplementedError()
        else:
            # Initialize empty data
            self.grid.fill(np.nan)

            # Get nearby traffic data
            df = pd.DataFrame.from_records(
                [v.to_dict(self.observer_vehicle) for v in self.env.road.vehicles]
            )
            # Normalize
            df = self.normalize(df)
            # Fill-in features
            for layer, feature in enumerate(self.features):
                if feature in df.columns:  # A vehicle feature
                    for _, vehicle in df[::-1].iterrows():
                        x, y = vehicle["x"], vehicle["y"]
                        # Recover unnormalized coordinates for cell index
                        if "x" in self.features_range:
                            x = utils.lmap(
                                x,
                                [-1, 1],
                                [
                                    self.features_range["x"][0],
                                    self.features_range["x"][1],
                                ],
                            )
                        if "y" in self.features_range:
                            y = utils.lmap(
                                y,
                                [-1, 1],
                                [
                                    self.features_range["y"][0],
                                    self.features_range["y"][1],
                                ],
                            )
                        cell = self.pos_to_index((x, y), relative=not self.absolute)
                        if (
                            0 <= cell[0] < self.grid.shape[-2]
                            and 0 <= cell[1] < self.grid.shape[-1]
                        ):
                            self.grid[layer, cell[0], cell[1]] = vehicle[feature]
                elif feature == "on_road":
                    self.fill_road_layer_by_lanes(layer)

            obs = self.grid

            if self.clip:
                obs = np.clip(obs, -1, 1)

            if self.as_image:
                obs = ((np.clip(obs, -1, 1) + 1) / 2 * 255).astype(np.uint8)

            obs = np.nan_to_num(obs).astype(self.space().dtype)

            return obs

    def pos_to_index(self, position: Vector, relative: bool = False) -> Tuple[int, int]:
        """
        Convert a world position to a grid cell index

        If align_to_vehicle_axes the cells are in the vehicle's frame, otherwise in the world frame.

        :param position: a world position
        :param relative: whether the position is already relative to the observer's position
        :return: the pair (i,j) of the cell index
        """
        if not relative:
            position -= self.observer_vehicle.position
        if self.align_to_vehicle_axes:
            c, s = np.cos(self.observer_vehicle.heading), np.sin(
                self.observer_vehicle.heading
            )
            position = np.array([[c, s], [-s, c]]) @ position
        return (
            int(np.floor((position[0] - self.grid_size[0, 0]) / self.grid_step[0])),
            int(np.floor((position[1] - self.grid_size[1, 0]) / self.grid_step[1])),
        )

    def index_to_pos(self, index: Tuple[int, int]) -> np.ndarray:
        position = np.array(
            [
                (index[0] + 0.5) * self.grid_step[0] + self.grid_size[0, 0],
                (index[1] + 0.5) * self.grid_step[1] + self.grid_size[1, 0],
            ]
        )

        if self.align_to_vehicle_axes:
            c, s = np.cos(-self.observer_vehicle.heading), np.sin(
                -self.observer_vehicle.heading
            )
            position = np.array([[c, s], [-s, c]]) @ position

        position += self.observer_vehicle.position
        return position

    def fill_road_layer_by_lanes(
        self, layer_index: int, lane_perception_distance: float = 100
    ) -> None:
        """
        A layer to encode the onroad (1) / offroad (0) information

        Here, we iterate over lanes and regularly placed waypoints on these lanes to fill the corresponding cells.
        This approach is faster if the grid is large and the road network is small.

        :param layer_index: index of the layer in the grid
        :param lane_perception_distance: lanes are rendered +/- this distance from vehicle location
        """
        lane_waypoints_spacing = np.amin(self.grid_step)
        road = self.env.road

        for _from in road.network.graph.keys():
            for _to in road.network.graph[_from].keys():
                for lane in road.network.graph[_from][_to]:
                    origin, _ = lane.local_coordinates(self.observer_vehicle.position)
                    waypoints = np.arange(
                        origin - lane_perception_distance,
                        origin + lane_perception_distance,
                        lane_waypoints_spacing,
                    ).clip(0, lane.length)
                    for waypoint in waypoints:
                        cell = self.pos_to_index(lane.position(waypoint, 0))
                        if (
                            0 <= cell[0] < self.grid.shape[-2]
                            and 0 <= cell[1] < self.grid.shape[-1]
                        ):
                            self.grid[layer_index, cell[0], cell[1]] = 1

    def fill_road_layer_by_cell(self, layer_index) -> None:
        """
        A layer to encode the onroad (1) / offroad (0) information

        In this implementation, we iterate the grid cells and check whether the corresponding world position
        at the center of the cell is onroad/offroad. This approach is faster if the grid is small and the road network large.
        """
        road = self.env.road
        for i, j in product(range(self.grid.shape[-2]), range(self.grid.shape[-1])):
            for _from in road.network.graph.keys():
                for _to in road.network.graph[_from].keys():
                    for lane in road.network.graph[_from][_to]:
                        if lane.on_lane(self.index_to_pos((i, j))):
                            self.grid[layer_index, i, j] = 1


class KinematicsGoalObservation(KinematicObservation):
    def __init__(self, env: "AbstractEnv", scales: List[float], **kwargs: dict) -> None:
        self.scales = np.array(scales)
        super().__init__(env, **kwargs)

    def space(self) -> spaces.Space:
        try:
            obs = self.observe()
            return spaces.Dict(
                dict(
                    desired_goal=spaces.Box(
                        -np.inf,
                        np.inf,
                        shape=obs["desired_goal"].shape,
                        dtype=np.float64,
                    ),
                    achieved_goal=spaces.Box(
                        -np.inf,
                        np.inf,
                        shape=obs["achieved_goal"].shape,
                        dtype=np.float64,
                    ),
                    observation=spaces.Box(
                        -np.inf,
                        np.inf,
                        shape=obs["observation"].shape,
                        dtype=np.float64,
                    ),
                )
            )
        except AttributeError:
            return spaces.Space()

    def observe(self) -> Dict[str, np.ndarray]:
        if not self.observer_vehicle:
            return OrderedDict(
                [
                    ("observation", np.zeros((len(self.features),))),
                    ("achieved_goal", np.zeros((len(self.features),))),
                    ("desired_goal", np.zeros((len(self.features),))),
                ]
            )

        obs = np.ravel(
            pd.DataFrame.from_records([self.observer_vehicle.to_dict()])[self.features]
        )
        goal = np.ravel(
            pd.DataFrame.from_records([self.observer_vehicle.goal.to_dict()])[
                self.features
            ]
        )
        obs = OrderedDict(
            [
                ("observation", obs / self.scales),
                ("achieved_goal", obs / self.scales),
                ("desired_goal", goal / self.scales),
            ]
        )
        return obs


class AttributesObservation(ObservationType):
    def __init__(
        self, env: "AbstractEnv", attributes: List[str], **kwargs: dict
    ) -> None:
        self.env = env
        self.attributes = attributes

    def space(self) -> spaces.Space:
        try:
            obs = self.observe()
            return spaces.Dict(
                {
                    attribute: spaces.Box(
                        -np.inf, np.inf, shape=obs[attribute].shape, dtype=np.float64
                    )
                    for attribute in self.attributes
                }
            )
        except AttributeError:
            return spaces.Space()

    def observe(self) -> Dict[str, np.ndarray]:
        return OrderedDict(
            [(attribute, getattr(self.env, attribute)) for attribute in self.attributes]
        )


class MultiAgentObservation(ObservationType):
    def __init__(self, env: "AbstractEnv", observation_config: dict, **kwargs) -> None:
        super().__init__(env)
        self.observation_config = observation_config
        self.agents_observation_types = []
        for vehicle in self.env.controlled_vehicles:
            obs_type = observation_factory(self.env, self.observation_config)
            obs_type.observer_vehicle = vehicle
            self.agents_observation_types.append(obs_type)

    def space(self) -> spaces.Space:
        return spaces.Tuple(
            [obs_type.space() for obs_type in self.agents_observation_types]
        )

    def observe(self) -> tuple:
        return tuple(obs_type.observe() for obs_type in self.agents_observation_types)


class TupleObservation(ObservationType):
    def __init__(
        self, env: "AbstractEnv", observation_configs: List[dict], **kwargs
    ) -> None:
        super().__init__(env)
        self.observation_types = [
            observation_factory(self.env, obs_config)
            for obs_config in observation_configs
        ]

    def space(self) -> spaces.Space:
        return spaces.Tuple([obs_type.space() for obs_type in self.observation_types])

    def observe(self) -> tuple:
        return tuple(obs_type.observe() for obs_type in self.observation_types)


class ExitObservation(KinematicObservation):

    """Specific to exit_env, observe the distance to the next exit lane as part of a KinematicObservation."""

    def observe(self) -> np.ndarray:
        if not self.env.road:
            return np.zeros(self.space().shape)

        # Add ego-vehicle
        ego_dict = self.observer_vehicle.to_dict()
        exit_lane = self.env.road.network.get_lane(("1", "2", -1))
        ego_dict["x"] = exit_lane.local_coordinates(self.observer_vehicle.position)[0]
        df = pd.DataFrame.from_records([ego_dict])[self.features]

        # Add nearby traffic
        close_vehicles = self.env.road.close_vehicles_to(
            self.observer_vehicle,
            self.env.PERCEPTION_DISTANCE,
            count=self.vehicles_count - 1,
            see_behind=self.see_behind,
        )
        if close_vehicles:
            origin = self.observer_vehicle if not self.absolute else None
            df = pd.concat(
                [
                    df,
                    pd.DataFrame.from_records(
                        [
                            v.to_dict(
                                origin, observe_intentions=self.observe_intentions
                            )
                            for v in close_vehicles[-self.vehicles_count + 1 :]
                        ]
                    )[self.features],
                ],
                ignore_index=True,
            )
        # Normalize and clip
        if self.normalize:
            df = self.normalize_obs(df)
        # Fill missing rows
        if df.shape[0] < self.vehicles_count:
            rows = np.zeros((self.vehicles_count - df.shape[0], len(self.features)))
            df = pd.concat(
                [df, pd.DataFrame(data=rows, columns=self.features)], ignore_index=True
            )
        # Reorder
        df = df[self.features]
        obs = df.values.copy()
        if self.order == "shuffled":
            self.env.np_random.shuffle(obs[1:])
        # Flatten
        return obs.astype(self.space().dtype)


class LidarObservation(ObservationType):
    DISTANCE = 0
    SPEED = 1

    def __init__(
        self,
        env,
        cells: int = 16,
        maximum_range: float = 60,
        normalize: bool = True,
        **kwargs
    ):
        super().__init__(env, **kwargs)
        self.cells = cells
        self.maximum_range = maximum_range
        self.normalize = normalize
        self.angle = 2 * np.pi / self.cells
        self.grid = np.ones((self.cells, 1)) * float("inf")
        self.origin = None

    def space(self) -> spaces.Space:
        high = 1 if self.normalize else self.maximum_range
        return spaces.Box(shape=(self.cells, 2), low=-high, high=high, dtype=np.float32)

    def observe(self) -> np.ndarray:
        obs = self.trace(
            self.observer_vehicle.position, self.observer_vehicle.velocity
        ).copy()
        if self.normalize:
            obs /= self.maximum_range
        return obs

    def trace(self, origin: np.ndarray, origin_velocity: np.ndarray) -> np.ndarray:
        self.origin = origin.copy()
        self.grid = np.ones((self.cells, 2)) * self.maximum_range

        for obstacle in self.env.road.vehicles + self.env.road.objects:
            if obstacle is self.observer_vehicle or not obstacle.solid:
                continue
            center_distance = np.linalg.norm(obstacle.position - origin)
            if center_distance > self.maximum_range:
                continue
            center_angle = self.position_to_angle(obstacle.position, origin)
            center_index = self.angle_to_index(center_angle)
            distance = center_distance - obstacle.WIDTH / 2
            if distance <= self.grid[center_index, self.DISTANCE]:
                direction = self.index_to_direction(center_index)
                velocity = (obstacle.velocity - origin_velocity).dot(direction)
                self.grid[center_index, :] = [distance, velocity]

            # Angular sector covered by the obstacle
            corners = utils.rect_corners(
                obstacle.position, obstacle.LENGTH, obstacle.WIDTH, obstacle.heading
            )
            angles = [self.position_to_angle(corner, origin) for corner in corners]
            min_angle, max_angle = min(angles), max(angles)
            if (
                min_angle < -np.pi / 2 < np.pi / 2 < max_angle
            ):  # Object's corners are wrapping around +pi
                min_angle, max_angle = max_angle, min_angle + 2 * np.pi
            start, end = self.angle_to_index(min_angle), self.angle_to_index(max_angle)
            if start < end:
                indexes = np.arange(start, end + 1)
            else:  # Object's corners are wrapping around 0
                indexes = np.hstack(
                    [np.arange(start, self.cells), np.arange(0, end + 1)]
                )

            # Actual distance computation for these sections
            for index in indexes:
                direction = self.index_to_direction(index)
                ray = [origin, origin + self.maximum_range * direction]
                distance = utils.distance_to_rect(ray, corners)
                if distance <= self.grid[index, self.DISTANCE]:
                    velocity = (obstacle.velocity - origin_velocity).dot(direction)
                    self.grid[index, :] = [distance, velocity]
        return self.grid

    def position_to_angle(self, position: np.ndarray, origin: np.ndarray) -> float:
        return (
            np.arctan2(position[1] - origin[1], position[0] - origin[0])
            + self.angle / 2
        )

    def position_to_index(self, position: np.ndarray, origin: np.ndarray) -> int:
        return self.angle_to_index(self.position_to_angle(position, origin))

    def angle_to_index(self, angle: float) -> int:
        return int(np.floor(angle / self.angle)) % self.cells

    def index_to_direction(self, index: int) -> np.ndarray:
        return np.array([np.cos(index * self.angle), np.sin(index * self.angle)])


def observation_factory(env: "AbstractEnv", config: dict) -> ObservationType:
    if config["type"] == "TimeToCollision":
        return TimeToCollisionObservation(env, **config)
    elif config["type"] == "Kinematics":
        return KinematicObservation(env, **config)
    elif config["type"] == "OccupancyGrid":
        return OccupancyGridObservation(env, **config)
    elif config["type"] == "KinematicsGoal":
        return KinematicsGoalObservation(env, **config)
    elif config["type"] == "GrayscaleObservation":
        return GrayscaleObservation(env, **config)
    elif config["type"] == "AttributesObservation":
        return AttributesObservation(env, **config)
    elif config["type"] == "MultiAgentObservation":
        return MultiAgentObservation(env, **config)
    elif config["type"] == "TupleObservation":
        return TupleObservation(env, **config)
    elif config["type"] == "LidarObservation":
        return LidarObservation(env, **config)
    elif config["type"] == "ExitObservation":
        return ExitObservation(env, **config)
    else:
        raise ValueError("Unknown observation type")
