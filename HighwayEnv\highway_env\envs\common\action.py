# 导入必要的库
import functools  # 用于函数式编程工具
import itertools  # 用于创建迭代器
from typing import TYPE_CHECKING, Callable, List, Optional, Tuple, Union  # 类型提示

import numpy as np
from gymnasium import spaces  # OpenAI Gym的空间定义

# 导入自定义模块
from highway_env import utils
from highway_env.utils import Vector
from highway_env.vehicle.controller import MDPVehicle
from highway_env.vehicle.dynamics import BicycleVehicle
from highway_env.vehicle.kinematics import Vehicle

if TYPE_CHECKING:
    from highway_env.envs.common.abstract import AbstractEnv

# 定义动作类型：可以是整数（离散动作）或numpy数组（连续动作）
Action = Union[int, np.ndarray]


class ActionType(object):
    """
    动作类型的基类，定义了动作空间的基本接口。
    每种动作类型都需要指定其定义空间，以及如何在环境中执行动作。
    """

    def __init__(self, env: "AbstractEnv", **kwargs) -> None:
        self.env = env  # 环境实例
        self.__controlled_vehicle = None  # 被控制的车辆

    def space(self) -> spaces.Space:
        """定义动作空间"""
        raise NotImplementedError

    @property
    def vehicle_class(self) -> Callable:
        """
        返回能够执行该动作的车辆类。
        必须返回Vehicle类或其子类。
        """
        raise NotImplementedError

    def act(self, action: Action) -> None:
        """
        在自我车辆上执行动作。
        大部分动作机制实际上是在vehicle.act(action)中实现的。
        可以根据ActionType的配置对动作进行预处理。

        参数:
            action: 要执行的动作
        """
        raise NotImplementedError

    def get_available_actions(self):
        """
        对于离散动作空间，返回可用动作的列表。
        """
        raise NotImplementedError

    @property
    def controlled_vehicle(self):
        """
        被控制的车辆。
        如果未设置，默认使用环境中的第一个车辆。
        """
        return self.__controlled_vehicle or self.env.vehicle

    @controlled_vehicle.setter
    def controlled_vehicle(self, vehicle):
        """设置被控制的车辆"""
        self.__controlled_vehicle = vehicle


class ContinuousAction(ActionType):
    """
    连续动作空间，用于油门和/或转向角控制。

    如果同时启用油门和转向，它们的顺序是：[油门, 转向]

    空间区间总是[-1, 1]，然后通过配置映射到实际的油门/转向区间。
    """

    ACCELERATION_RANGE = (-5, 5.0)
    """加速度范围：[-x, x]，单位：m/s²"""

    STEERING_RANGE = (-np.pi / 4, np.pi / 4)
    """转向角度范围：[-x, x]，单位：弧度（±45度）"""

    def __init__(
        self,
        env: "AbstractEnv",
        acceleration_range: Optional[Tuple[float, float]] = None,  # 可选的加速度范围
        steering_range: Optional[Tuple[float, float]] = None,      # 可选的转向范围
        speed_range: Optional[Tuple[float, float]] = None,         # 可选的速度范围
        longitudinal: bool = True,    # 是否启用纵向控制（油门）
        lateral: bool = True,         # 是否启用横向控制（转向）
        dynamical: bool = False,      # 是否使用动力学模型而不是运动学
        clip: bool = True,            # 是否裁剪动作到定义范围
        **kwargs
    ) -> None:
        """
        创建连续动作空间。

        参数:
            env: 环境实例
            acceleration_range: 加速度范围 [m/s²]
            steering_range: 转向范围 [rad]
            speed_range: 可达到的速度范围 [m/s]
            longitudinal: 是否启用油门控制
            lateral: 是否启用转向控制
            dynamical: 是否模拟动力学（如摩擦）而不是运动学
            clip: 是否将动作裁剪到定义范围内
        """
        super().__init__(env)
        # 如果未指定范围，使用默认值
        self.acceleration_range = (
            acceleration_range if acceleration_range else self.ACCELERATION_RANGE
        )
        self.steering_range = steering_range if steering_range else self.STEERING_RANGE
        self.speed_range = speed_range
        self.lateral = lateral
        self.longitudinal = longitudinal
        # 至少要启用一种控制
        if not self.lateral and not self.longitudinal:
            raise ValueError(
                "必须启用纵向和/或横向控制"
            )
        self.dynamical = dynamical
        self.clip = clip
        # 动作空间维度：如果同时启用转向和油门则为2，否则为1
        self.size = 2 if self.lateral and self.longitudinal else 1
        self.last_action = np.zeros(self.size)  # 记录上一次的动作

    def space(self) -> spaces.Box:
        """
        返回动作空间。
        使用Box空间，范围为[-1,1]，维度由self.size决定
        """
        return spaces.Box(-1.0, 1.0, shape=(self.size,), dtype=np.float32)

    @property
    def vehicle_class(self) -> Callable:
        """根据是否使用动力学模型返回相应的车辆类"""
        return Vehicle if not self.dynamical else BicycleVehicle

    def get_action(self, action: np.ndarray):
        """
        处理动作值。
        将[-1,1]范围的动作值映射到实际的物理范围。

        参数:
            action: 原始动作值，范围[-1,1]
        返回:
            处理后的动作字典，包含实际的加速度和转向角
        """
        # 如果启用裁剪，确保动作在[-1,1]范围内
        if self.clip:
            action = np.clip(action, -1, 1)
        # 如果指定了速度范围，设置车辆的速度限制
        if self.speed_range:
            (
                self.controlled_vehicle.MIN_SPEED,
                self.controlled_vehicle.MAX_SPEED,
            ) = self.speed_range
        # 根据启用的控制类型返回相应的动作
        if self.longitudinal and self.lateral:
            # 同时控制加速和转向
            return {
                "acceleration": utils.lmap(action[0], [-1, 1], self.acceleration_range),
                "steering": utils.lmap(action[1], [-1, 1], self.steering_range),
            }
        elif self.longitudinal:
            # 只控制加速
            return {
                "acceleration": utils.lmap(action[0], [-1, 1], self.acceleration_range),
                "steering": 0,
            }
        elif self.lateral:
            # 只控制转向
            return {
                "acceleration": 0,
                "steering": utils.lmap(action[0], [-1, 1], self.steering_range),
            }

    def act(self, action: np.ndarray) -> None:
        """
        执行动作。
        先将动作转换为实际的物理值，然后让车辆执行。

        参数:
            action: 原始动作值
        """
        self.controlled_vehicle.act(self.get_action(action))
        self.last_action = action


class DiscreteAction(ContinuousAction):
    """
    离散动作空间。
    通过将连续动作空间离散化来实现。
    """
    def __init__(
        self,
        env: "AbstractEnv",
        acceleration_range: Optional[Tuple[float, float]] = None,
        steering_range: Optional[Tuple[float, float]] = None,
        longitudinal: bool = True,
        lateral: bool = True,
        dynamical: bool = False,
        clip: bool = True,
        actions_per_axis: int = 3,  # 每个轴的离散动作数量
        **kwargs
    ) -> None:
        """
        创建离散动作空间。
        继承自连续动作空间，但将动作空间离散化。

        额外参数:
            actions_per_axis: 每个维度的离散动作数量
        """
        super().__init__(
            env,
            acceleration_range=acceleration_range,
            steering_range=steering_range,
            longitudinal=longitudinal,
            lateral=lateral,
            dynamical=dynamical,
            clip=clip,
        )
        self.actions_per_axis = actions_per_axis

    def space(self) -> spaces.Discrete:
        """
        返回离散动作空间。
        空间大小为每个轴的动作数量的size次方。
        """
        return spaces.Discrete(self.actions_per_axis**self.size)

    def act(self, action: int) -> None:
        """
        执行离散动作。
        将离散动作索引转换为连续动作值，然后执行。

        参数:
            action: 离散动作的索引
        """
        # 获取连续动作空间
        cont_space = super().space()
        # 将每个维度均匀分割为指定数量的值
        axes = np.linspace(cont_space.low, cont_space.high, self.actions_per_axis).T
        # 生成所有可能的动作组合
        all_actions = list(itertools.product(*axes))
        # 执行选定的动作
        super().act(all_actions[action])


class DiscreteMetaAction(ActionType):
    """
    离散元动作空间：包括变道和巡航控制设定点。
    这是一个更高层次的动作空间，动作包括：变道、加速、减速等。
    """

    # 所有可用动作的映射
    ACTIONS_ALL = {0: "LANE_LEFT", 1: "IDLE", 2: "LANE_RIGHT", 3: "FASTER", 4: "SLOWER"}
    """动作索引到标签的映射"""

    # 纵向动作（速度控制）的映射
    ACTIONS_LONGI = {0: "SLOWER", 1: "IDLE", 2: "FASTER"}
    """纵向动作索引到标签的映射"""

    # 横向动作（变道）的映射
    ACTIONS_LAT = {0: "LANE_LEFT", 1: "IDLE", 2: "LANE_RIGHT"}
    """横向动作索引到标签的映射"""

    def __init__(
        self,
        env: "AbstractEnv",
        longitudinal: bool = True,  # 是否包含纵向动作
        lateral: bool = True,       # 是否包含横向动作
        target_speeds: Optional[Vector] = None,  # 目标速度列表
        **kwargs
    ) -> None:
        """
        创建离散元动作空间。

        参数:
            env: 环境实例
            longitudinal: 是否包含纵向动作
            lateral: 是否包含横向动作
            target_speeds: 车辆可以跟踪的速度列表
        """
        super().__init__(env)
        self.longitudinal = longitudinal
        self.lateral = lateral
        # 设置目标速度，如果未指定则使用默认值
        self.target_speeds = (
            np.array(target_speeds)
            if target_speeds is not None
            else MDPVehicle.DEFAULT_TARGET_SPEEDS
        )
        # 根据启用的控制类型选择动作集
        self.actions = (
            self.ACTIONS_ALL  # 所有动作
            if longitudinal and lateral
            else self.ACTIONS_LONGI  # 只有纵向动作
            if longitudinal
            else self.ACTIONS_LAT  # 只有横向动作
            if lateral
            else None
        )
        if self.actions is None:
            raise ValueError(
                "至少要包含纵向或横向动作"
            )
        # 创建动作标签到索引的反向映射
        self.actions_indexes = {v: k for k, v in self.actions.items()}

    def space(self) -> spaces.Space:
        """返回离散动作空间，大小为动作数量"""
        return spaces.Discrete(len(self.actions))

    @property
    def vehicle_class(self) -> Callable:
        """返回能够执行元动作的车辆类"""
        return functools.partial(MDPVehicle, target_speeds=self.target_speeds)

    def act(self, action: Union[int, np.ndarray]) -> None:
        """
        执行元动作。
        将动作索引转换为相应的命令并执行。

        参数:
            action: 动作索引
        """
        self.controlled_vehicle.act(self.actions[int(action)])

    def get_available_actions(self) -> List[int]:
        """
        获取当前可用的动作列表。

        在道路边界不能变道，
        在最高或最低速度时不能加速或减速。

        返回:
            可用动作的索引列表
        """
        # 空闲动作总是可用的
        actions = [self.actions_indexes["IDLE"]]
        network = self.controlled_vehicle.road.network
        # 检查可能的变道动作
        for l_index in network.side_lanes(self.controlled_vehicle.lane_index):
            # 检查是否可以向左变道
            if (
                l_index[2] < self.controlled_vehicle.lane_index[2]
                and network.get_lane(l_index).is_reachable_from(
                    self.controlled_vehicle.position
                )
                and self.lateral
            ):
                actions.append(self.actions_indexes["LANE_LEFT"])
            # 检查是否可以向右变道
            if (
                l_index[2] > self.controlled_vehicle.lane_index[2]
                and network.get_lane(l_index).is_reachable_from(
                    self.controlled_vehicle.position
                )
                and self.lateral
            ):
                actions.append(self.actions_indexes["LANE_RIGHT"])
        # 检查是否可以加速
        if (
            self.controlled_vehicle.speed_index
            < self.controlled_vehicle.target_speeds.size - 1
            and self.longitudinal
        ):
            actions.append(self.actions_indexes["FASTER"])
        # 检查是否可以减速
        if self.controlled_vehicle.speed_index > 0 and self.longitudinal:
            actions.append(self.actions_indexes["SLOWER"])
        return actions


class MultiAgentAction(ActionType):
    """
    多智能体动作空间。
    用于控制多个车辆的情况。
    """
    def __init__(self, env: "AbstractEnv", action_config: dict, **kwargs) -> None:
        """
        创建多智能体动作空间。

        参数:
            env: 环境实例
            action_config: 动作配置字典
        """
        super().__init__(env)
        self.action_config = action_config
        self.agents_action_types = []
        # 为每个被控制的车辆创建动作类型
        for vehicle in self.env.controlled_vehicles:
            action_type = action_factory(self.env, self.action_config)
            action_type.controlled_vehicle = vehicle
            self.agents_action_types.append(action_type)

    def space(self) -> spaces.Space:
        """返回多智能体动作空间，是每个智能体动作空间的元组"""
        return spaces.Tuple(
            [action_type.space() for action_type in self.agents_action_types]
        )

    @property
    def vehicle_class(self) -> Callable:
        """返回车辆类"""
        return action_factory(self.env, self.action_config).vehicle_class

    def act(self, action: Action) -> None:
        """
        执行多智能体动作。
        为每个智能体执行相应的动作。

        参数:
            action: 动作元组，每个元素对应一个智能体的动作
        """
        assert isinstance(action, tuple)
        for agent_action, action_type in zip(action, self.agents_action_types):
            action_type.act(agent_action)

    def get_available_actions(self):
        """返回所有智能体可用动作的笛卡尔积"""
        return itertools.product(
            *[
                action_type.get_available_actions()
                for action_type in self.agents_action_types
            ]
        )


def action_factory(env: "AbstractEnv", config: dict) -> ActionType:
    """
    动作类型工厂函数。
    根据配置创建相应的动作类型实例。

    参数:
        env: 环境实例
        config: 配置字典，包含动作类型和参数

    返回:
        ActionType的实例
    """
    if config["type"] == "ContinuousAction":
        return ContinuousAction(env, **config)
    if config["type"] == "DiscreteAction":
        return DiscreteAction(env, **config)
    elif config["type"] == "DiscreteMetaAction":
        return DiscreteMetaAction(env, **config)
    elif config["type"] == "MultiAgentAction":
        return MultiAgentAction(env, **config)
    else:
        raise ValueError("未知的动作类型")
