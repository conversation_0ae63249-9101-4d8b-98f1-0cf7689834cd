import gymnasium as gym
import highway_env
import numpy as np
from algorithms.environments import RawMultiAgentEnv

class Highway_MultiAgent_Env(RawMultiAgentEnv):
    """
    Highway multi-agent environment wrapper for xuance.
    """
    def __init__(self, config):
        super(Highway_MultiAgent_Env, self).__init__()
        # 创建环境
        self.env_id = config.env_id  # 如 "highway-v0", "merge-v0" 等
        self.env = gym.make(self.env_id, render_mode=config.render_mode)
        
        # 配置为多智能体模式
        env_config = {
            "observation": {
                "type": "Kinematics",
                "vehicles_count": 15,
                "features": ["presence", "x", "y", "vx", "vy", "cos_h", "sin_h"],
                "normalize": True
            },
            "action": {
                "type": "DiscreteMetaAction" if not config.continuous_action else "ContinuousAction"
            },
            "duration": 40,
            "simulation_frequency": 15,
            "policy_frequency": 5,
            "controlled_vehicles": 3,  # 控制多个车辆
        }
        self.env.unwrapped.configure(env_config)
        
        # 设置多智能体属性
        self.num_agents = self.env.unwrapped.config["controlled_vehicles"]
        self.agents = [f"agent_{i}" for i in range(self.num_agents)]
        
        # 为每个智能体创建观察和动作空间
        self.observation_space = {agent: self.env.observation_space for agent in self.agents}
        self.action_space = {agent: self.env.action_space for agent in self.agents}
        
        self.metadata = self.env.metadata
        self.max_episode_steps = self.env.unwrapped.config["duration"]
        self._episode_step = 0
        self.individual_episode_reward = {k: 0.0 for k in self.agents}

    def close(self):
        """关闭环境"""
        self.env.close()

    def render(self, *args):
        """渲染环境"""
        return self.env.render()

    def reset(self):
        """重置环境"""
        all_obs, info = self.env.reset()
        # 将观察分配给每个智能体
        observations = {f"agent_{i}": all_obs[i] for i in range(self.num_agents)}
        
        self._episode_step = 0
        for agent_key in self.agents:
            self.individual_episode_reward[agent_key] = 0.0
            
        reset_info = {"infos": info,
                      "individual_episode_rewards": self.individual_episode_reward}
        return observations, reset_info

    def step(self, actions_dict):
        """执行动作"""
        # 将字典形式的动作转换为列表
        actions_list = [actions_dict[f"agent_{i}"] for i in range(self.num_agents)]
        
        # 执行动作
        all_obs, all_rewards, terminated, truncated, info = self.env.step(actions_list)
        
        # 将结果转换为字典形式
        observations = {f"agent_{i}": all_obs[i] for i in range(self.num_agents)}
        rewards = {f"agent_{i}": all_rewards[i] for i in range(self.num_agents)}
        terminated_dict = {f"agent_{i}": terminated for i in range(self.num_agents)}
        
        # 更新累计奖励
        for k, v in rewards.items():
            self.individual_episode_reward[k] += v
            
        self._episode_step += 1
        truncated = True if self._episode_step >= self.max_episode_steps else truncated
        
        # 添加额外信息
        if terminated or truncated:
            info["episode_step"] = self._episode_step
            info["episode_score"] = self.individual_episode_reward.copy()
            
            # 重置环境时返回的观察
            reset_all_obs, reset_info = self.env.reset()
            info["reset_obs"] = {f"agent_{i}": reset_all_obs[i] for i in range(self.num_agents)}
            
        step_info = {"infos": info,
                     "individual_episode_rewards": self.individual_episode_reward}
                     
        return observations, rewards, terminated_dict, truncated, step_info

    def agent_mask(self):
        """返回智能体掩码"""
        return {agent: True for agent in self.agents}

    def avail_actions(self):
        """返回可用动作掩码"""
        if isinstance(self.env.action_space, gym.spaces.Discrete):
            return {agent: np.ones(self.action_space[agent].n, np.bool_) for agent in self.agents}
        return None