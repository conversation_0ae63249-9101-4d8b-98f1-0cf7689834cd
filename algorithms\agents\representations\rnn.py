# ================================================================================================
# RNN（循环神经网络）表征网络实现
#
# 这个文件实现了基于循环神经网络的表征网络，用于处理序列数据和部分可观察环境：
# 1. Basic_RNN - 基础RNN网络，支持LSTM和GRU两种变体
#
# RNN适用场景：
# - 部分可观察环境（POMDP）：智能体无法观察到完整状态
# - 序列决策任务：需要记忆历史信息的任务
# - 时序相关的强化学习问题
#
# RNN vs MLP/CNN：
# - RNN有记忆能力，可以处理时序信息
# - MLP/CNN是无记忆的，只处理当前观察
# ================================================================================================

from algorithms.utils import Sequence, Optional, Union, Callable, Tuple  # 类型注解
from algorithms.agents import Module, Tensor         # 神经网络模块基类
import torch                                         # PyTorch深度学习框架
from torch import nn                                 # PyTorch神经网络模块
from algorithms.agents.utils import mlp_block, gru_block, lstm_block, ModuleType  # 网络构建工具


class Basic_RNN(Module):
    """
    基础循环神经网络表征网络

    这是专门用于处理序列数据和部分可观察环境的RNN网络。
    结合了MLP预处理层和RNN记忆层，能够处理需要历史信息的强化学习任务。

    🏗️ **网络结构**：
    观察输入 → MLP预处理 → RNN记忆层 → 特征输出

    🎯 **使用场景**：
    - **部分可观察环境**：智能体无法看到完整状态
    - **序列决策**：需要记住历史动作和观察
    - **时序相关任务**：当前决策依赖于历史信息

    🧠 **RNN类型**：
    - **LSTM**：长短期记忆网络，适合长序列
    - **GRU**：门控循环单元，计算更高效

    📊 **典型配置**：
    - fc_hidden_sizes: [128] (MLP预处理层)
    - recurrent_hidden_size: 256 (RNN隐藏状态大小)
    - N_recurrent_layers: 1 (RNN层数)

    Args:
        input_shape (Sequence[int]): 输入观察的形状
        hidden_sizes (dict): 网络结构配置字典
            - fc_hidden_sizes: MLP层的隐藏单元数列表
            - recurrent_hidden_size: RNN隐藏状态大小
        normalize (Optional[Module]): 归一化层
        initialize (Optional[Callable]): 权重初始化方法
        activation (Optional[ModuleType]): 激活函数
        device (Optional[Union[str, int, torch.device]]): 计算设备
        **kwargs: 其他参数
            - N_recurrent_layers: RNN层数
            - dropout: Dropout概率
            - rnn: RNN类型 ("LSTM" 或 "GRU")
    """

    def __init__(self,
                 input_shape: Sequence[int],
                 hidden_sizes: dict,
                 normalize: Optional[Module] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None,
                 **kwargs):
        """
        初始化RNN表征网络

        Args:
            input_shape: 输入形状
            hidden_sizes: 包含网络结构的字典
            **kwargs: 包含RNN特定参数
        """
        super(Basic_RNN, self).__init__()

        # 保存基本配置
        self.input_shape = input_shape
        self.fc_hidden_sizes = hidden_sizes["fc_hidden_sizes"]          # MLP层大小
        self.recurrent_hidden_size = hidden_sizes["recurrent_hidden_size"]  # RNN隐藏状态大小

        # RNN特定配置
        self.N_recurrent_layer = kwargs["N_recurrent_layers"]           # RNN层数
        self.dropout = kwargs["dropout"]                                # Dropout概率
        self.lstm = True if kwargs["rnn"] == "LSTM" else False         # 是否使用LSTM（否则使用GRU）

        # 网络组件配置
        self.normalize = normalize
        self.initialize = initialize
        self.activation = activation
        self.device = device

        # 设置输出形状（RNN隐藏状态的大小）
        self.output_shapes = {'state': (hidden_sizes["recurrent_hidden_size"],)}

        # 构建网络结构：MLP预处理 + RNN记忆层
        self.mlp, self.rnn, output_dim = self._create_network()

        # 设置归一化层（如果需要）
        if self.normalize is not None:
            self.use_normalize = True
            self.input_norm = self.normalize(input_shape, device=device)    # 输入归一化
            self.norm_rnn = self.normalize(output_dim, device=device)       # RNN输出归一化
        else:
            self.use_normalize = False

    def _create_network(self) -> Tuple[Module, Module, int]:
        """
        创建RNN网络结构

        构建两部分网络：
        1. MLP预处理层：对输入观察进行特征提取
        2. RNN记忆层：处理序列信息和维持记忆

        Returns:
            Tuple[Module, Module, int]: (MLP网络, RNN网络, 输出维度)
        """
        # 构建MLP预处理层
        layers = []
        input_shape = self.input_shape

        # 为每个全连接层创建MLP块
        for h in self.fc_hidden_sizes:
            mlp_layer, input_shape = mlp_block(
                input_shape[0],      # 输入维度
                h,                   # 输出维度
                self.normalize,      # 归一化层
                self.activation,     # 激活函数
                self.initialize,     # 初始化方法
                device=self.device   # 计算设备
            )
            layers.extend(mlp_layer)

        # 构建RNN记忆层
        if self.lstm:
            # 使用LSTM：有细胞状态，适合长序列
            rnn_layer, input_shape = lstm_block(
                input_shape[0],              # 输入维度
                self.recurrent_hidden_size,  # 隐藏状态大小
                self.N_recurrent_layer,      # RNN层数
                self.dropout,                # Dropout概率
                self.initialize,             # 初始化方法
                self.device                  # 计算设备
            )
        else:
            # 使用GRU：无细胞状态，计算更高效
            rnn_layer, input_shape = gru_block(
                input_shape[0],              # 输入维度
                self.recurrent_hidden_size,  # 隐藏状态大小
                self.N_recurrent_layer,      # RNN层数
                self.dropout,                # Dropout概率
                self.initialize,             # 初始化方法
                self.device                  # 计算设备
            )

        return nn.Sequential(*layers), rnn_layer, input_shape

    def forward(self, x: Tensor, h: Tensor, c: Tensor = None):
        """
        前向传播：处理序列输入并更新隐藏状态

        Args:
            x (Tensor): 输入观察，形状 [batch_size, seq_len, input_dim]
            h (Tensor): 隐藏状态，形状 [num_layers, batch_size, hidden_size]
            c (Tensor): 细胞状态（仅LSTM使用），形状同隐藏状态

        Returns:
            dict: 包含以下键的字典：
                - state: RNN输出特征
                - rnn_hidden: 更新后的隐藏状态
                - rnn_cell: 更新后的细胞状态（LSTM）或None（GRU）
        """
        # 输入预处理和归一化
        if self.use_normalize:
            tensor_x = self.input_norm(torch.as_tensor(x, dtype=torch.float32, device=self.device))
        else:
            tensor_x = torch.as_tensor(x, dtype=torch.float32, device=self.device)

        # 通过MLP预处理层
        mlp_output = self.mlp(tensor_x)

        # 优化RNN参数存储（提高计算效率）
        self.rnn.flatten_parameters()

        # 根据RNN类型进行前向传播
        if self.lstm:
            # LSTM：需要隐藏状态和细胞状态
            output, (hn, cn) = self.rnn(mlp_output, (h, c))
            if self.use_normalize:
                output = self.norm_rnn(output)
            return {
                "state": output,
                "rnn_hidden": hn.detach(),  # 分离梯度，避免反向传播过长
                "rnn_cell": cn.detach()
            }
        else:
            # GRU：只需要隐藏状态
            output, hn = self.rnn(mlp_output, h)
            if self.use_normalize:
                output = self.norm_rnn(output)
            return {
                "state": output,
                "rnn_hidden": hn.detach(),  # 分离梯度
                "rnn_cell": None
            }

    def init_hidden(self, batch):
        hidden_states = torch.zeros(size=(self.N_recurrent_layer, batch, self.recurrent_hidden_size)).to(self.device)
        cell_states = torch.zeros_like(hidden_states).to(self.device) if self.lstm else None
        return hidden_states, cell_states

    def init_hidden_item(self, indexes: list, *rnn_hidden):
        zeros_size = (self.N_recurrent_layer, len(indexes), self.recurrent_hidden_size)
        if self.lstm:
            rnn_hidden[0][:, indexes] = torch.zeros(size=zeros_size).to(self.device)
            rnn_hidden[1][:, indexes] = torch.zeros(size=zeros_size).to(self.device)
            return rnn_hidden
        else:
            rnn_hidden[0][:, indexes] = torch.zeros(size=zeros_size).to(self.device)
            return rnn_hidden

    def get_hidden_item(self, i, *rnn_hidden):
        return (rnn_hidden[0][:, i], rnn_hidden[1][:, i]) if self.lstm else (rnn_hidden[0][:, i], None)
