# 导入抽象方法装饰器和可选类型
from abc import abstractmethod
from typing import Optional

# 导入numpy用于数值计算
import numpy as np
# 导入gymnasium的基础环境类
from gymnasium import Env

# 导入自定义的抽象环境基类
from highway_env.envs.common.abstract import AbstractEnv
# 导入观察空间相关的工具
from highway_env.envs.common.observation import (
    MultiAgentObservation,  # 多智能体观察
    observation_factory,    # 观察空间工厂函数
)
# 导入道路相关的类
from highway_env.road.lane import LineType, StraightLane  # 车道线类型和直线车道
from highway_env.road.road import Road, RoadNetwork        # 道路和道路网络
# 导入车辆相关的类
from highway_env.vehicle.graphics import VehicleGraphics   # 车辆图形
from highway_env.vehicle.kinematics import Vehicle         # 车辆运动学
from highway_env.vehicle.objects import Landmark, Obstacle # 地标和障碍物


class GoalEnv(Env):
    """
    基于目标的环境接口。
    
    这个接口主要用于支持如Stable Baseline3's HER (Hindsight Experience Replay)等算法。
    观察空间必须包含三个元素：
    - observation: 环境的实际观察值
    - desired_goal: 智能体应该尝试达到的目标
    - achieved_goal: 当前实际达到的目标
    """

    @abstractmethod
    def compute_reward(
        self, 
        achieved_goal: np.ndarray,  # 实际达到的目标状态
        desired_goal: np.ndarray,   # 期望达到的目标状态
        info: dict                  # 额外信息字典
    ) -> float:
        """
        计算奖励值。基于期望目标和实际达到的目标计算奖励。
        
        参数:
            achieved_goal: 实际达到的目标
            desired_goal: 期望达到的目标
            info: 包含额外信息的字典
        返回:
            float: 奖励值
        """
        raise NotImplementedError


class ParkingEnv(AbstractEnv, GoalEnv):
    """
    停车环境 - 一个连续控制任务
    
    这是一个到达类型的任务，智能体观察自己的位置和速度，
    必须通过控制加速度和转向角度来到达指定的停车位。
    """

    # 用于灰度观察的配置
    PARKING_OBS = {
        "observation": {
            "type": "KinematicsGoal",  # 观察类型：运动学目标
            "features": ["x", "y", "vx", "vy", "cos_h", "sin_h"],  # 状态特征：位置、速度、朝向
            "scales": [100, 100, 5, 5, 1, 1],  # 各特征的缩放系数
            "normalize": False,  # 是否归一化
        }
    }

    def __init__(self, config: dict = None, render_mode: Optional[str] = None) -> None:
        """
        初始化停车环境
        
        参数:
            config: 环境配置字典
            render_mode: 渲染模式
        """
        super().__init__(config, render_mode)  # 调用父类初始化方法
        self.observation_type_parking = None   # 停车观察类型初始化为空

    @classmethod
    def default_config(cls) -> dict:
        """
        默认环境配置
        
        返回一个包含所有环境参数默认值的字典，包括：
        - 观察空间配置：运动学状态
        - 动作空间类型：连续动作
        - 奖励权重和参数
        - 环境物理参数
        - 渲染参数
        """
        config = super().default_config()  # 获取父类的默认配置
        config.update(
            {
                "observation": {
                    "type": "KinematicsGoal",  # 观察类型：运动学目标
                    "features": ["x", "y", "vx", "vy", "cos_h", "sin_h"],  # 状态特征
                    "scales": [100, 100, 5, 5, 1, 1],  # 特征缩放系数
                    "normalize": False,  # 是否归一化
                },
                "action": {
                    "type": "ContinuousAction"  # 动作类型：连续动作空间
                },
                "reward_weights": [1, 0.3, 0, 0, 0.02, 0.02],  # 奖励计算的权重 [x, y, vx, vy, cos_h, sin_h]
                "success_goal_reward": 0.12,  # 成功达到目标的奖励阈值
                "collision_reward": -5,       # 碰撞惩罚
                "steering_range": np.deg2rad(45),  # 最大转向角度：45度
                "simulation_frequency": 15,   # 物理模拟频率
                "policy_frequency": 5,        # 策略控制频率
                "duration": 100,              # 最大步数
                "screen_width": 600,          # 屏幕宽度
                "screen_height": 300,         # 屏幕高度
                "centering_position": [0.5, 0.5],  # 居中位置
                "scaling": 7,                      # 缩放系数
                "controlled_vehicles": 1,     # 可控制的车辆数量
                "vehicles_count": 0,          # 其他车辆数量
                "add_walls": True,            # 是否添加墙壁
            }
        )
        return config

    def define_spaces(self) -> None:
        """
        设置观察空间和动作空间的类型
        基于配置文件定义具体的空间类型
        """
        super().define_spaces()  # 调用父类的空间定义方法
        # 创建停车专用的观察类型
        self.observation_type_parking = observation_factory(
            self, self.PARKING_OBS["observation"]
        )

    def _info(self, obs, action) -> dict:
        """
        生成环境信息字典
        
        参数:
            obs: 当前观察
            action: 执行的动作
        返回:
            包含环境额外信息的字典，主要是成功状态
        """
        info = super(ParkingEnv, self)._info(obs, action)  # 获取基础信息
        # 处理多智能体情况
        if isinstance(self.observation_type, MultiAgentObservation):
            success = tuple(
                self._is_success(agent_obs["achieved_goal"], agent_obs["desired_goal"])
                for agent_obs in obs
            )
        else:
            # 单智能体情况
            obs = self.observation_type_parking.observe()
            success = self._is_success(obs["achieved_goal"], obs["desired_goal"])
        info.update({"is_success": success})  # 更新成功状态
        return info

    def _reset(self):
        """
        重置环境到初始状态
        创建道路网络和车辆
        """
        self._create_road()      # 创建道路
        self._create_vehicles()  # 创建车辆

    def _create_road(self, spots: int = 14) -> None:
        """
        创建停车场道路网络
        
        参数:
            spots: 停车位数量，默认14个
        """
        net = RoadNetwork()  # 创建道路网络对象
        width = 4.0         # 车道宽度
        lt = (LineType.CONTINUOUS, LineType.CONTINUOUS)  # 实线车道线类型
        x_offset = 0       # x方向偏移量
        y_offset = 10      # y方向偏移量
        length = 8         # 车道长度
        
        # 创建平行的停车位
        for k in range(spots):
            # 计算每个停车位的x坐标
            x = (k + 1 - spots // 2) * (width + x_offset) - width / 2
            
            # 添加上方车道
            net.add_lane(
                "a",  # 起始节点
                "b",  # 终止节点
                StraightLane(
                    [x, y_offset],           # 起点坐标
                    [x, y_offset + length],  # 终点坐标
                    width=width,             # 车道宽度
                    line_types=lt            # 车道线类型
                ),
            )
            
            # 添加下方车道
            net.add_lane(
                "b",  # 起始节点
                "c",  # 终止节点
                StraightLane(
                    [x, -y_offset],            # 起点坐标
                    [x, -y_offset - length],   # 终点坐标
                    width=width,               # 车道宽度
                    line_types=lt              # 车道线类型
                ),
            )

        # 创建道路对象
        self.road = Road(
            network=net,                                        # 道路网络
            np_random=self.np_random,                          # 随机数生成器
            record_history=self.config["show_trajectories"],   # 是否记录轨迹
        )

    def _create_vehicles(self) -> None:
        """
        创建车辆和目标
        
        包括：
        1. 创建可控制的车辆（玩家车辆）
        2. 设置目标停车位
        3. 添加其他静止车辆
        4. 添加边界墙壁
        """
        # 获取所有可用的停车位
        empty_spots = list(self.road.network.lanes_dict().keys())

        # 1. 创建可控制车辆（玩家车辆）
        self.controlled_vehicles = []
        for i in range(self.config["controlled_vehicles"]):
            # 计算初始x坐标
            x0 = (i - self.config["controlled_vehicles"] // 2) * 10
            # 创建车辆，随机朝向
            vehicle = self.action_type.vehicle_class(
                self.road,                                    # 道路对象
                [x0, 0],                                     # 初始位置
                2 * np.pi * self.np_random.uniform(),        # 随机朝向
                0                                            # 初始速度
            )
            vehicle.color = VehicleGraphics.EGO_COLOR        # 设置车辆颜色
            self.road.vehicles.append(vehicle)               # 添加到道路车辆列表
            self.controlled_vehicles.append(vehicle)         # 添加到控制车辆列表
            empty_spots.remove(vehicle.lane_index)          # 从空位中移除

        # 2. 设置目标位置
        for vehicle in self.controlled_vehicles:
            # 随机选择一个空车位作为目标
            lane_index = empty_spots[self.np_random.choice(np.arange(len(empty_spots)))]
            lane = self.road.network.get_lane(lane_index)
            # 创建目标标记
            vehicle.goal = Landmark(
                self.road,                     # 道路对象
                lane.position(lane.length / 2, 0),  # 目标位置（车道中点）
                heading=lane.heading               # 目标朝向
            )
            self.road.objects.append(vehicle.goal)  # 添加目标到道路对象列表
            empty_spots.remove(lane_index)          # 从空位中移除

        # 3. 创建其他车辆
        for i in range(self.config["vehicles_count"]):
            if not empty_spots:  # 如果没有空位了就跳出
                continue
            # 随机选择一个空位
            lane_index = empty_spots[self.np_random.choice(np.arange(len(empty_spots)))]
            # 创建静止的车辆
            v = Vehicle.make_on_lane(self.road, lane_index, 4, speed=0)
            self.road.vehicles.append(v)  # 添加到道路车辆列表
            empty_spots.remove(lane_index)  # 从空位中移除

        # 4. 添加墙壁
        if self.config["add_walls"]:
            width, height = 70, 42  # 设置墙壁的宽度和高度
            
            # 添加上下墙
            for y in [-height / 2, height / 2]:  # 上下墙的y坐标
                obstacle = Obstacle(self.road, [0, y])  # 创建障碍物
                obstacle.LENGTH, obstacle.WIDTH = (width, 1)  # 设置尺寸
                # 计算对角线长度（用于碰撞检测）
                obstacle.diagonal = np.sqrt(obstacle.LENGTH**2 + obstacle.WIDTH**2)
                self.road.objects.append(obstacle)  # 添加到道路对象列表
            
            # 添加左右墙
            for x in [-width / 2, width / 2]:  # 左右墙的x坐标
                obstacle = Obstacle(self.road, [x, 0], heading=np.pi / 2)  # 创建垂直障碍物
                obstacle.LENGTH, obstacle.WIDTH = (height, 1)  # 设置尺寸
                # 计算对角线长度（用于碰撞检测）
                obstacle.diagonal = np.sqrt(obstacle.LENGTH**2 + obstacle.WIDTH**2)
                self.road.objects.append(obstacle)  # 添加到道路对象列表

    def compute_reward(
        self,
        achieved_goal: np.ndarray,  # 实际达到的目标状态
        desired_goal: np.ndarray,   # 期望的目标状态
        info: dict,                 # 额外信息
        p: float = 0.5,            # Lp范数的参数
    ) -> float:
        """
        计算奖励值
        
        使用加权p范数计算目标距离，距离越近奖励越高
        p < 1 时会使得奖励在[0,1]范围内具有高峰度
        
        参数:
            achieved_goal: 实际达到的目标状态
            desired_goal: 期望的目标状态
            info: 额外信息
            p: Lp范数的参数，默认0.5
        返回:
            float: 奖励值，负值，绝对值越小表示越接近目标
        """
        # 计算实际状态和目标状态的加权差异
        return -np.power(
            np.dot(
                np.abs(achieved_goal - desired_goal),  # 计算各维度的绝对差
                np.array(self.config["reward_weights"]),  # 各维度的权重
            ),
            p,  # 使用p范数
        )

    def _reward(self, action: np.ndarray) -> float:
        """
        计算总奖励值
        
        包括：
        1. 基于目标距离的奖励
        2. 碰撞惩罚
        
        参数:
            action: 执行的动作
        返回:
            float: 总奖励值
        """
        # 获取观察值
        obs = self.observation_type_parking.observe()
        obs = obs if isinstance(obs, tuple) else (obs,)
        
        # 计算基于目标的奖励
        reward = sum(
            self.compute_reward(
                agent_obs["achieved_goal"], agent_obs["desired_goal"], {}
            )
            for agent_obs in obs
        )
        
        # 添加碰撞惩罚
        reward += self.config["collision_reward"] * sum(
            v.crashed for v in self.controlled_vehicles
        )
        
        return reward

    def _is_success(self, achieved_goal: np.ndarray, desired_goal: np.ndarray) -> bool:
        """
        判断是否成功达到目标
        
        当计算的奖励值大于成功阈值时判定为成功
        
        参数:
            achieved_goal: 实际达到的目标状态
            desired_goal: 期望的目标状态
        返回:
            bool: 是否成功
        """
        return (
            self.compute_reward(achieved_goal, desired_goal, {})
            > -self.config["success_goal_reward"]
        )

    def _is_terminated(self) -> bool:
        """
        判断回合是否结束
        
        当发生以下情况之一时结束：
        1. 车辆发生碰撞
        2. 成功达到目标
        
        返回:
            bool: 是否结束
        """
        # 检查是否发生碰撞
        crashed = any(vehicle.crashed for vehicle in self.controlled_vehicles)
        
        # 检查是否达到目标
        obs = self.observation_type_parking.observe()
        obs = obs if isinstance(obs, tuple) else (obs,)
        success = all(
            self._is_success(agent_obs["achieved_goal"], agent_obs["desired_goal"])
            for agent_obs in obs
        )
        
        return bool(crashed or success)

    def _is_truncated(self) -> bool:
        """
        判断回合是否因为超时而终止
        
        返回:
            bool: 是否超时
        """
        return self.time >= self.config["duration"]


class ParkingEnvActionRepeat(ParkingEnv):
    """降低控制频率的停车环境变体"""
    def __init__(self):
        super().__init__({"policy_frequency": 1, "duration": 20})


class ParkingEnvParkedVehicles(ParkingEnv):
    """包含更多停泊车辆的停车环境变体"""
    def __init__(self):
        super().__init__({"vehicles_count": 10})
