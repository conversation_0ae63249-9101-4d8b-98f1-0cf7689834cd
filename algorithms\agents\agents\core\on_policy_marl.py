# ================================================================================================
# 多智能体在策略强化学习（MARL On-Policy）核心基类
#
# 这个文件实现了多智能体在策略强化学习算法的基础框架，主要用于：
# 1. MAPPO (Multi-Agent PPO) 算法
# 2. MAA2C (Multi-Agent A2C) 算法
# 3. 其他基于策略梯度的多智能体在策略算法
#
# 多智能体在策略算法的特点：
# - 多个智能体同时学习和交互
# - 支持中心化训练、去中心化执行（CTDE）
# - 处理部分可观察性和智能体间协调
# - 支持参数共享和独立学习两种模式
# ================================================================================================

import numpy as np                                    # 数值计算库
import torch                                         # PyTorch 深度学习框架
from argparse import Namespace                       # 命名空间类，用于配置管理
from tqdm import tqdm                               # 进度条显示库
from copy import deepcopy                           # 深拷贝函数
from operator import itemgetter                     # 操作符工具，用于数据提取
from algorithms.utils import List, MARL_OnPolicyBuffer, MARL_OnPolicyBuffer_RNN, Optional, Union  # 多智能体缓冲区和类型注解
from algorithms.environments import DummyVecMultiAgentEnv, SubprocVecMultiAgentEnv  # 多智能体向量化环境
from algorithms.agents import Module                 # 神经网络模块基类
from algorithms.agents.agents.base import MARLAgents, BaseCallback  # 多智能体基类和回调函数


class OnPolicyMARLAgents(MARLAgents):
    """
    多智能体在策略强化学习的核心基类

    这是所有多智能体在策略算法（如MAPPO、MAA2C）的基础类，提供了：
    1. 多智能体经验缓冲区管理
    2. 协调多个智能体的数据收集和处理
    3. 支持中心化训练、去中心化执行（CTDE）
    4. 处理智能体间的信息共享和协调

    多智能体在策略算法的工作流程：
    1. 所有智能体同时与环境交互收集经验
    2. 使用全局状态信息进行中心化价值估计
    3. 计算每个智能体的优势函数和回报
    4. 同时更新所有智能体的策略网络

    Args:
        config (Namespace): 配置参数，包含超参数和多智能体设置
        envs (Union[DummyVecMultiAgentEnv, SubprocVecMultiAgentEnv]): 多智能体向量化环境
        callback (Optional[BaseCallback]): 用户自定义回调函数，用于：
            - 多智能体训练过程的监控和日志记录
            - 智能体间协调策略的可视化
            - 自定义的多智能体训练逻辑
    """

    def __init__(self,
                 config: Namespace,
                 envs: Union[DummyVecMultiAgentEnv, SubprocVecMultiAgentEnv],
                 callback: Optional[BaseCallback] = None):
        """
        初始化多智能体在策略学习器

        设置多智能体在策略算法所需的核心参数和组件
        """
        # 调用父类初始化方法
        super(OnPolicyMARLAgents, self).__init__(config, envs, callback)

        # ========================================================================================
        # 多智能体在策略算法的核心参数
        # ========================================================================================

        # 标识这是在策略算法
        self.on_policy = True

        # 是否为连续控制任务（如机器人控制）
        self.continuous_control: bool = False

        # 训练轮数：每批数据要训练多少个epoch
        self.n_epochs = config.n_epochs

        # 小批次数量：将缓冲区数据分成多少个mini-batch
        self.n_minibatch = config.n_minibatch

        # 缓冲区大小：存储多少步的多智能体经验
        self.buffer_size = self.config.buffer_size

        # 批次大小：每个mini-batch的大小
        self.batch_size = self.buffer_size // self.n_minibatch

        # 多智能体经验缓冲区：支持RNN和普通两种类型
        self.memory: Optional[Union[MARL_OnPolicyBuffer, MARL_OnPolicyBuffer_RNN]] = None

    def _build_memory(self):
        """
        构建多智能体经验缓冲区

        创建专门用于多智能体在策略算法的经验缓冲区，支持：
        1. 多智能体观察和动作的存储
        2. 全局状态信息的管理（用于中心化训练）
        3. 动作掩码的处理（用于离散动作空间）
        4. GAE计算和优势函数归一化

        Returns:
            Union[MARL_OnPolicyBuffer, MARL_OnPolicyBuffer_RNN]: 多智能体经验缓冲区
        """
        # 处理动作掩码：如果使用动作掩码，为每个智能体创建掩码形状
        if self.use_actions_mask:
            avail_actions_shape = {key: (self.action_space[key].n,) for key in self.agent_keys}
        else:
            avail_actions_shape = None

        # 构建缓冲区初始化参数
        input_buffer = dict(
            agent_keys=self.agent_keys,                                    # 智能体标识列表
            state_space=self.state_space if self.use_global_state else None,  # 全局状态空间（CTDE）
            obs_space=self.observation_space,                              # 观察空间（每个智能体）
            act_space=self.action_space,                                   # 动作空间（每个智能体）
            n_envs=self.n_envs,                                           # 并行环境数量
            buffer_size=self.config.buffer_size,                          # 缓冲区大小
            use_gae=self.config.use_gae,                                  # 是否使用GAE
            use_advnorm=self.config.use_advnorm,                          # 是否使用优势归一化
            gamma=self.config.gamma,                                      # 折扣因子
            gae_lam=self.config.gae_lambda,                              # GAE lambda参数
            avail_actions_shape=avail_actions_shape,                      # 动作掩码形状
            use_actions_mask=self.use_actions_mask,                       # 是否使用动作掩码
            max_episode_steps=self.episode_length                         # 最大回合步数
        )

        # 根据是否使用RNN选择合适的缓冲区类型
        Buffer = MARL_OnPolicyBuffer_RNN if self.use_rnn else MARL_OnPolicyBuffer
        return Buffer(**input_buffer)

    def _build_policy(self) -> Module:
        """
        构建多智能体策略网络

        这是一个抽象方法，需要在子类中实现具体的多智能体策略网络构建逻辑。
        不同的多智能体算法（如MAPPO、MAA2C）会有不同的网络结构。

        Returns:
            Module: 多智能体策略网络模块
        """
        raise NotImplementedError

    def store_experience(self, obs_dict, avail_actions, actions_dict, log_pi_a, rewards_dict, values_dict,
                         terminals_dict, info, **kwargs):
        """
        将多智能体经验数据存储到缓冲区

        这个方法负责收集和组织所有智能体的经验数据，包括观察、动作、奖励等，
        并将其存储到多智能体经验缓冲区中，用于后续的策略更新。

        Args:
            obs_dict (List[dict]): 每个智能体的观察数据
            avail_actions (List[dict]): 每个智能体的可用动作掩码
            actions_dict (List[dict]): 每个智能体执行的动作
            log_pi_a (dict): 动作的对数概率（用于重要性采样）
            rewards_dict (List[dict]): 每个智能体获得的奖励
            values_dict (dict): 每个智能体的状态价值估计
            terminals_dict (List[dict]): 每个智能体的终止状态标志
            info (List[dict]): 环境的额外信息
            **kwargs: 其他输入，如全局状态等
        """
        # 组织多智能体经验数据
        experience_data = {
            # 观察数据：为每个智能体收集观察
            'obs': {k: np.array([data[k] for data in obs_dict]) for k in self.agent_keys},

            # 动作数据：为每个智能体收集执行的动作
            'actions': {k: np.array([data[k] for data in actions_dict]) for k in self.agent_keys},

            # 旧策略的对数概率：用于PPO的重要性采样比率计算
            'log_pi_old': log_pi_a,

            # 奖励数据：为每个智能体收集获得的奖励
            'rewards': {k: np.array([data[k] for data in rewards_dict]) for k in self.agent_keys},

            # 价值估计：每个智能体的状态价值V(s)
            'values': values_dict,

            # 终止标志：标识哪些智能体的回合已结束
            'terminals': {k: np.array([data[k] for data in terminals_dict]) for k in self.agent_keys},

            # 智能体掩码：标识哪些智能体在当前步骤是活跃的
            'agent_mask': {k: np.array([data['agent_mask'][k] for data in info]) for k in self.agent_keys},
        }

        # 如果使用RNN，添加回合步数信息
        if self.use_rnn:
            experience_data['episode_steps'] = np.array([data['episode_step'] - 1 for data in info])

        # 如果使用全局状态，添加全局状态信息（用于中心化训练）
        if self.use_global_state:
            experience_data['state'] = np.array(kwargs['state'])

        # 如果使用动作掩码，添加可用动作信息
        if self.use_actions_mask:
            experience_data['avail_actions'] = {k: np.array([data[k] for data in avail_actions])
                                                for k in self.agent_keys}

        # 将经验数据存储到缓冲区
        self.memory.store(**experience_data)

    def init_rnn_hidden(self, n_envs):
        """
        初始化RNN隐藏状态

        如果使用基于RNN的表示网络，为所有智能体初始化隐藏状态。
        这对于处理部分可观察环境和序列决策非常重要。

        Args:
            n_envs (int): 并行环境的数量

        Returns:
            tuple: (actor_hidden_states, critic_hidden_states)
                - actor_hidden_states: 演员网络的RNN隐藏状态
                - critic_hidden_states: 评论家网络的RNN隐藏状态
        """
        rnn_hidden_actor, rnn_hidden_critic = None, None

        if self.use_rnn:
            # 计算批次大小：考虑参数共享的情况
            batch = n_envs * self.n_agents if self.use_parameter_sharing else n_envs

            # 为每个模型键初始化演员网络的隐藏状态
            rnn_hidden_actor = {k: self.policy.actor_representation[k].init_hidden(batch)
                               for k in self.model_keys}

            # 为每个模型键初始化评论家网络的隐藏状态
            rnn_hidden_critic = {k: self.policy.critic_representation[k].init_hidden(batch)
                                for k in self.model_keys}

        return rnn_hidden_actor, rnn_hidden_critic

    def init_hidden_item(self,
                         i_env: int,
                         rnn_hidden_actor: Optional[dict] = None,
                         rnn_hidden_critic: Optional[dict] = None):
        """
        为特定环境初始化RNN隐藏状态

        当某个环境的回合结束时，需要重置该环境对应的RNN隐藏状态。
        这确保了新回合开始时RNN状态的正确初始化。

        Args:
            i_env (int): 要重置的环境索引
            rnn_hidden_actor (Optional[dict]): 演员网络的RNN隐藏状态
            rnn_hidden_critic (Optional[dict]): 评论家网络的RNN隐藏状态

        Returns:
            tuple: 重置后的(actor_hidden_states, critic_hidden_states)
        """
        assert self.use_rnn is True, "只有在使用RNN时才能调用此方法"

        # 根据是否使用参数共享确定批次索引
        if self.use_parameter_sharing:
            # 参数共享：每个环境包含多个智能体
            b_index = np.arange(i_env * self.n_agents, (i_env + 1) * self.n_agents)
        else:
            # 独立参数：每个环境对应一个索引
            b_index = [i_env, ]

        # 重置演员网络的隐藏状态
        for k in self.model_keys:
            rnn_hidden_actor[k] = self.policy.actor_representation[k].init_hidden_item(
                b_index, *rnn_hidden_actor[k])

        # 如果没有评论家隐藏状态，直接返回
        if rnn_hidden_critic is None:
            return rnn_hidden_actor, None

        # 重置评论家网络的隐藏状态
        for k in self.model_keys:
            rnn_hidden_critic[k] = self.policy.critic_representation[k].init_hidden_item(
                b_index, *rnn_hidden_critic[k])

        return rnn_hidden_actor, rnn_hidden_critic

    def action(self,
               obs_dict: List[dict],
               state: Optional[np.ndarray] = None,
               avail_actions_dict: Optional[List[dict]] = None,
               rnn_hidden_actor: Optional[dict] = None,
               rnn_hidden_critic: Optional[dict] = None,
               test_mode: Optional[bool] = False,
               **kwargs):
        """
        🎯 多智能体动作选择的核心方法

        根据所有智能体的观察和当前策略，为每个智能体选择动作。
        支持参数共享和独立学习两种模式，以及RNN和前馈网络。

        Args:
            obs_dict (List[dict]): 每个智能体的观察数据
                格式: [{agent1: obs1, agent2: obs2, ...}, ...]
            state (Optional[np.ndarray]): 全局状态信息（用于中心化训练）
            avail_actions_dict (Optional[List[dict]]): 可用动作掩码
                用于离散动作空间，标识哪些动作是合法的
            rnn_hidden_actor (Optional[dict]): 演员网络的RNN隐藏状态
            rnn_hidden_critic (Optional[dict]): 评论家网络的RNN隐藏状态
            test_mode (Optional[bool]): 是否为测试模式
                - True: 确定性动作选择（无噪声）
                - False: 随机动作选择（用于探索）
            **kwargs: 其他参数

        Returns:
            tuple: 包含以下元素的元组：
                - rnn_hidden_actor_new (dict): 更新后的演员RNN隐藏状态
                - rnn_hidden_critic_new (dict): 更新后的评论家RNN隐藏状态
                - actions_dict (dict): 每个智能体选择的动作
                - log_pi_a (dict): 动作的对数概率（用于PPO的重要性采样）
                - values_dict (dict): 状态价值估计（仅在训练模式下）
        """
        # 获取环境数量和初始化输出变量
        n_env = len(obs_dict)
        rnn_hidden_critic_new, values_out, log_pi_a_dict, values_dict = {}, {}, {}, {}

        # ========================================================================================
        # 阶段1: 构建网络输入并获取策略分布
        # ========================================================================================

        # 构建网络输入：将多智能体观察转换为网络可处理的格式
        obs_input, agents_id, avail_actions_input = self._build_inputs(obs_dict, avail_actions_dict)

        # 通过演员网络获取策略分布和更新的RNN隐藏状态
        rnn_hidden_actor_new, pi_dists = self.policy(observation=obs_input,
                                                     agent_ids=agents_id,
                                                     avail_actions=avail_actions_input,
                                                     rnn_hidden=rnn_hidden_actor)

        # 如果不是测试模式，获取状态价值估计
        if not test_mode:
            rnn_hidden_critic_new, values_out = self.policy.get_values(observation=obs_input,
                                                                       agent_ids=agents_id,
                                                                       rnn_hidden=rnn_hidden_critic)

        # ========================================================================================
        # 阶段2: 根据参数共享模式处理动作采样和输出格式化
        # ========================================================================================

        if self.use_parameter_sharing:
            # 参数共享模式：所有智能体使用相同的网络参数
            key = self.agent_keys[0]  # 使用第一个智能体的键作为代表

            # 从策略分布中采样动作
            actions_sample = pi_dists[key].stochastic_sample()

            # 根据控制类型重塑动作形状
            if self.continuous_control:
                # 连续控制：动作是连续值向量
                actions_out = actions_sample.reshape(n_env, self.n_agents, -1)
            else:
                # 离散控制：动作是离散索引
                actions_out = actions_sample.reshape(n_env, self.n_agents)

            # 将动作转换为字典格式：[{agent1: action1, agent2: action2, ...}, ...]
            actions_dict = [{k: actions_out[e, i].cpu().detach().numpy()
                           for i, k in enumerate(self.agent_keys)} for e in range(n_env)]

            # 如果不是测试模式，计算对数概率和价值
            if not test_mode:
                # 计算动作的对数概率（用于PPO的重要性采样）
                log_pi_a = pi_dists[key].log_prob(actions_sample).cpu().detach().numpy().reshape(n_env, self.n_agents)
                log_pi_a_dict = {k: log_pi_a[:, i] for i, k in enumerate(self.agent_keys)}

                # 格式化价值估计
                values_out[key] = values_out[key].reshape(n_env, self.n_agents)
                values_dict = {k: values_out[key][:, i].cpu().detach().numpy()
                              for i, k in enumerate(self.agent_keys)}
        else:
            # 独立参数模式：每个智能体使用独立的网络参数

            # 为每个智能体独立采样动作
            actions_sample = {k: pi_dists[k].stochastic_sample() for k in self.agent_keys}

            # 根据控制类型格式化动作输出
            if self.continuous_control:
                # 连续控制：保持向量形状
                actions_dict = [{k: actions_sample[k].cpu().detach().numpy()[e].reshape([-1])
                               for k in self.agent_keys} for e in range(n_env)]
            else:
                # 离散控制：转换为标量
                actions_dict = [{k: actions_sample[k].cpu().detach().numpy()[e].reshape([])
                               for k in self.agent_keys} for e in range(n_env)]

            # 如果不是测试模式，计算对数概率和价值
            if not test_mode:
                # 为每个智能体计算对数概率
                log_pi_a = {k: pi_dists[k].log_prob(actions_sample[k]).cpu().detach().numpy()
                           for k in self.agent_keys}
                log_pi_a_dict = {k: log_pi_a[k].reshape([n_env]) for i, k in enumerate(self.agent_keys)}

                # 为每个智能体格式化价值估计
                values_dict = {k: values_out[k].cpu().detach().numpy().reshape([n_env])
                              for k in self.agent_keys}

        # 返回所有输出信息
        return {"rnn_hidden_actor": rnn_hidden_actor_new, "rnn_hidden_critic": rnn_hidden_critic_new,
                "actions": actions_dict, "log_pi": log_pi_a_dict, "values": values_dict}

    def values_next(self,
                    i_env: int,
                    obs_dict: dict,
                    state: Optional[np.ndarray] = None,
                    rnn_hidden_critic: Optional[dict] = None):
        """
        计算特定环境的下一状态价值

        当某个环境的回合结束时，需要计算该环境中所有智能体的最终状态价值，
        用于GAE计算和优势函数估计。这对于准确的价值估计非常重要。

        Args:
            i_env (int): 环境索引，标识哪个并行环境
            obs_dict (dict): 该环境中每个智能体的最终观察
            state (Optional[np.ndarray]): 全局状态信息（如果使用）
            rnn_hidden_critic (Optional[dict]): 评论家网络的RNN隐藏状态

        Returns:
            tuple: 包含以下元素：
                - rnn_hidden_critic_new (dict): 更新后的评论家RNN隐藏状态
                - values_dict (dict): 每个智能体的状态价值估计
        """
        n_env = 1
        rnn_hidden_critic_i = None
        if self.use_parameter_sharing:
            key = self.agent_keys[0]
            if self.use_rnn:
                hidden_item_index = np.arange(i_env * self.n_agents, (i_env + 1) * self.n_agents)
                rnn_hidden_critic_i = {key: self.policy.critic_representation[key].get_hidden_item(
                    hidden_item_index, *rnn_hidden_critic[key])}
                batch_size = n_env * self.n_agents
                obs_array = np.array(itemgetter(*self.agent_keys)(obs_dict))
                obs_input = {key: obs_array.reshape([batch_size, 1, -1])}
                agents_id = torch.eye(self.n_agents).unsqueeze(0).expand(n_env, -1, -1).reshape(batch_size, 1, -1).to(
                    self.device)
            else:
                obs_input = {key: np.array([itemgetter(*self.agent_keys)(obs_dict)])}
                agents_id = torch.eye(self.n_agents).unsqueeze(0).expand(n_env, -1, -1).to(self.device)

            rnn_hidden_critic_new, values_out = self.policy.get_values(observation=obs_input,
                                                                       agent_ids=agents_id,
                                                                       rnn_hidden=rnn_hidden_critic_i)
            values_out = values_out[key].reshape(self.n_agents)
            values_dict = {k: values_out[i].cpu().detach().numpy() for i, k in enumerate(self.agent_keys)}

        else:
            if self.use_rnn:
                rnn_hidden_critic_i = {k: self.policy.critic_representation[k].get_hidden_item(
                    [i_env, ], *rnn_hidden_critic[k]) for k in self.agent_keys}
            obs_input = {k: obs_dict[k][None, :] for k in self.agent_keys} if self.use_rnn else obs_dict

            rnn_hidden_critic_new, values_out = self.policy.get_values(observation=obs_input,
                                                                       rnn_hidden=rnn_hidden_critic_i)
            values_dict = {k: values_out[k].cpu().detach().numpy().reshape([]) for k in self.agent_keys}

        return rnn_hidden_critic_new, values_dict

    def train(self, n_steps):
        """
        🚀 多智能体在策略算法的主训练循环

        这是整个多智能体在策略强化学习的核心方法，实现了完整的训练流程：
        1. 多智能体数据收集：所有智能体同时与环境交互
        2. 经验存储：将多智能体经验存储到缓冲区
        3. 协调训练：同时更新所有智能体的策略
        4. 状态管理：处理RNN隐藏状态和环境重置

        Args:
            n_steps (int): 训练步数（实际是更新轮数）

        Returns:
            dict: 训练信息统计
        """
        train_info = {}

        # ========================================================================================
        # RNN模式的训练流程
        # ========================================================================================
        if self.use_rnn:
            with tqdm(total=n_steps) as process_bar:
                step_start, step_last = deepcopy(self.current_step), deepcopy(self.current_step)
                n_steps_all = n_steps * self.n_envs

                # RNN模式：按回合收集数据
                while step_last - step_start < n_steps_all:
                    # 运行完整回合收集经验数据
                    self.run_episodes(None, n_episodes=self.n_envs, test_mode=False)

                    # 执行多轮策略更新
                    update_info = self.train_epochs(n_epochs=self.n_epochs)
                    self.log_infos(update_info, self.current_step)
                    train_info.update(update_info)

                    # 调用训练轮次结束回调
                    self.callback.on_train_epochs_end(self.current_step, policy=self.policy, memory=self.memory,
                                                      current_episode=self.current_episode, n_steps=n_steps,
                                                      update_info=update_info)

                    # 更新进度条
                    process_bar.update((self.current_step - step_last) // self.n_envs)
                    step_last = deepcopy(self.current_step)

                process_bar.update(n_steps - process_bar.last_print_n)
                self.callback.on_train_step_end(self.current_step, envs=self.envs, policy=self.policy,
                                                n_steps=n_steps, train_info=train_info)
            return train_info

        # ========================================================================================
        # 前馈网络模式的训练流程
        # ========================================================================================

        # 获取初始状态
        obs_dict = self.envs.buf_obs
        avail_actions = self.envs.buf_avail_actions if self.use_actions_mask else None
        state = self.envs.buf_state if self.use_global_state else None

        # 主训练循环
        for _ in tqdm(range(n_steps)):
            # ========================================================================================
            # 阶段1: 多智能体动作选择和环境交互
            # ========================================================================================

            # 为所有智能体选择动作
            policy_out = self.action(obs_dict=obs_dict, state=state, avail_actions_dict=avail_actions, test_mode=False)
            actions_dict, log_pi_a_dict = policy_out['actions'], policy_out['log_pi']
            values_dict = policy_out['values']

            # 在环境中执行多智能体动作
            next_obs_dict, rewards_dict, terminated_dict, truncated, info = self.envs.step(actions_dict)
            next_state = self.envs.buf_state if self.use_global_state else None
            next_avail_actions = self.envs.buf_avail_actions if self.use_actions_mask else None

            # 调用训练步骤回调函数
            self.callback.on_train_step(self.current_step, envs=self.envs, policy=self.policy,
                                        obs=obs_dict, policy_out=policy_out, acts=actions_dict, next_obs=next_obs_dict,
                                        rewards=rewards_dict, state=state, next_state=next_state,
                                        avail_actions=avail_actions, next_avail_actions=next_avail_actions,
                                        terminals=terminated_dict, truncations=truncated, infos=info,
                                        n_steps=n_steps, values_dict=values_dict)

            # ========================================================================================
            # 阶段2: 经验存储和缓冲区管理
            # ========================================================================================

            # 存储多智能体经验到缓冲区
            self.store_experience(obs_dict, avail_actions, actions_dict, log_pi_a_dict, rewards_dict, values_dict,
                                  terminated_dict, info, **{'state': state})

            # 当缓冲区满时，计算价值估计并完成路径
            if self.memory.full:
                for i in range(self.n_envs):
                    if all(terminated_dict[i].values()):
                        # 所有智能体都终止：价值为0
                        value_next = {key: 0.0 for key in self.agent_keys}
                    else:
                        # 部分智能体未终止：计算下一状态价值
                        next_state_i = next_state[i] if self.use_global_state else None
                        _, value_next = self.values_next(i_env=i, obs_dict=next_obs_dict[i], state=next_state_i)
                    self.memory.finish_path(i_env=i, value_next=value_next,
                                            value_normalizer=self.learner.value_normalizer)

            # ========================================================================================
            # 阶段3: 策略更新
            # ========================================================================================

            # 执行多轮训练更新
            update_info = self.train_epochs(n_epochs=self.n_epochs)
            self.log_infos(update_info, self.current_step)
            train_info.update(update_info)

            # 更新状态为下一步准备
            obs_dict, avail_actions = deepcopy(next_obs_dict), deepcopy(next_avail_actions)
            state = deepcopy(next_state) if self.use_global_state else None

            # ========================================================================================
            # 阶段4: 环境重置处理
            # ========================================================================================

            # 处理环境重置（回合结束）
            for i in range(self.n_envs):
                if all(terminated_dict[i].values()) or truncated[i]:
                    if all(terminated_dict[i].values()):
                        # 正常终止：价值为0
                        value_next = {key: 0.0 for key in self.agent_keys}
                    else:
                        # 截断终止：计算当前状态价值
                        state_i = state[i] if self.use_global_state else None
                        _, value_next = self.values_next(i_env=i, obs_dict=obs_dict[i], state=state_i)

                    # 完成该环境的路径
                    self.memory.finish_path(i_env=i, value_next=value_next,
                                            value_normalizer=self.learner.value_normalizer)

                    # 重置环境观察
                    obs_dict[i] = info[i]["reset_obs"]
                    self.envs.buf_obs[i] = info[i]["reset_obs"]
                    # 重置相关状态信息
                    if self.use_actions_mask:
                        avail_actions[i] = info[i]["reset_avail_actions"]
                        self.envs.buf_avail_actions[i] = info[i]["reset_avail_actions"]
                    if self.use_global_state:
                        state[i] = info[i]["reset_state"]
                        self.envs.buf_state[i] = info[i]["reset_state"]

                    # 更新回合计数
                    self.current_episode[i] += 1

                    # 记录回合统计信息
                    if self.use_wandb:
                        episode_info = {
                            f"Train-Results/Episode-Steps/rank_{self.rank}/env-%d" % i: info[i]["episode_step"],
                            f"Train-Results/Episode-Rewards/rank_{self.rank}/env-%d" % i: info[i]["episode_score"]
                        }
                    else:
                        episode_info = {
                            f"Train-Results/Episode-Steps/rank_{self.rank}": {"env-%d" % i: info[i]["episode_step"]},
                            f"Train-Results/Episode-Rewards/rank_{self.rank}": {
                                "env-%d" % i: np.mean(itemgetter(*self.agent_keys)(info[i]["episode_score"]))}
                        }

                    # 记录和更新训练信息
                    self.log_infos(episode_info, self.current_step)
                    train_info.update(episode_info)

                    # 调用回合信息回调
                    self.callback.on_train_episode_info(envs=self.envs, policy=self.policy, env_id=i,
                                                        infos=info, rank=self.rank, use_wandb=self.use_wandb,
                                                        current_step=self.current_step,
                                                        current_episode=self.current_episode,
                                                        n_steps=n_steps)

            # 更新全局步数计数器
            self.current_step += self.n_envs

            # 调用训练步骤结束回调
            self.callback.on_train_step_end(self.current_step, envs=self.envs, policy=self.policy,
                                            n_steps=n_steps, train_info=train_info)

        # 返回训练统计信息
        return train_info

    def run_episodes(self, env_fn=None, n_episodes: int = 1, test_mode: bool = False):
        """
        Run some episodes when use RNN.

        Parameters:
            env_fn: The function that can make some testing environments.
            n_episodes (int): Number of episodes.
            test_mode (bool): Whether to test the model.

        Returns:
            Scores: The episode scores.
        """
        envs = self.envs if env_fn is None else env_fn()
        num_envs = envs.num_envs
        videos, episode_videos, images = [[] for _ in range(num_envs)], [], None
        _current_episode, _current_step, scores, best_score = 0, 0, [], -np.inf
        obs_dict, info = envs.reset()
        avail_actions = envs.buf_avail_actions if self.use_actions_mask else None
        state = envs.buf_state if self.use_global_state else None
        if test_mode:
            if self.config.render_mode == "rgb_array" and self.render:
                images = envs.render(self.config.render_mode)
                for idx, img in enumerate(images):
                    videos[idx].append(img)
        else:
            if self.use_rnn:
                self.memory.clear_episodes()
        rnn_hidden_actor, rnn_hidden_critic = self.init_rnn_hidden(num_envs)

        while _current_episode < n_episodes:
            policy_out = self.action(obs_dict=obs_dict, state=state, avail_actions_dict=avail_actions,
                                     rnn_hidden_actor=rnn_hidden_actor, rnn_hidden_critic=rnn_hidden_critic,
                                     test_mode=test_mode)
            rnn_hidden_actor, rnn_hidden_critic = policy_out['rnn_hidden_actor'], policy_out['rnn_hidden_critic']
            actions_dict, log_pi_a_dict = policy_out['actions'], policy_out['log_pi']
            values_dict = policy_out['values']
            next_obs_dict, rewards_dict, terminated_dict, truncated, info = envs.step(actions_dict)
            next_state = envs.buf_state if self.use_global_state else None
            next_avail_actions = envs.buf_avail_actions if self.use_actions_mask else None
            if test_mode:
                if self.config.render_mode == "rgb_array" and self.render:
                    images = envs.render(self.config.render_mode)
                    for idx, img in enumerate(images):
                        videos[idx].append(img)
            else:
                self.store_experience(obs_dict, avail_actions, actions_dict, log_pi_a_dict, rewards_dict, values_dict,
                                      terminated_dict, info, **{'state': state})

            self.callback.on_test_step(envs=envs, policy=self.policy, images=images, test_mode=test_mode,
                                       obs=obs_dict, policy_out=policy_out, acts=actions_dict,
                                       next_obs=next_obs_dict, rewards=rewards_dict,
                                       terminals=terminated_dict, truncations=truncated, infos=info,
                                       state=state, next_state=next_state,
                                       current_train_step=self.current_step, n_episodes=n_episodes,
                                       current_step=_current_step, current_episode=_current_episode)

            obs_dict, avail_actions = deepcopy(next_obs_dict), deepcopy(next_avail_actions)
            state = deepcopy(next_state) if self.use_global_state else None

            for i in range(num_envs):
                if all(terminated_dict[i].values()) or truncated[i]:
                    _current_episode += 1
                    episode_score = float(np.mean(itemgetter(*self.agent_keys)(info[i]["episode_score"])))
                    scores.append(episode_score)
                    if test_mode:
                        if self.use_rnn:
                            rnn_hidden_actor, _ = self.init_hidden_item(i, rnn_hidden_actor)
                        if best_score < episode_score:
                            best_score = episode_score
                            episode_videos = videos[i].copy()
                        if self.config.test_mode:
                            print("Episode: %d, Score: %.2f" % (_current_episode, episode_score))
                    else:
                        if all(terminated_dict[i].values()):
                            value_next = {key: 0.0 for key in self.agent_keys}
                        else:
                            _, value_next = self.values_next(i_env=i, obs_dict=obs_dict[i],
                                                             state=None if state is None else state[i],
                                                             rnn_hidden_critic=rnn_hidden_critic)
                        self.memory.finish_path(i_env=i, i_step=info[i]['episode_step'], value_next=value_next,
                                                value_normalizer=self.learner.value_normalizer)
                        if self.use_rnn:
                            rnn_hidden_actor, rnn_hidden_critic = self.init_hidden_item(i, rnn_hidden_actor,
                                                                                        rnn_hidden_critic)
                        if self.use_wandb:
                            episode_info = {
                                "Train-Results/Episode-Steps/env-%d" % i: info[i]["episode_step"],
                                "Train-Results/Episode-Rewards/env-%d" % i: info[i]["episode_score"]
                            }
                        else:
                            episode_info = {
                                "Train-Results/Episode-Steps": {"env-%d" % i: info[i]["episode_step"]},
                                "Train-Results/Episode-Rewards": {
                                    "env-%d" % i: np.mean(itemgetter(*self.agent_keys)(info[i]["episode_score"]))}
                            }
                        self.current_step += info[i]["episode_step"]
                        self.log_infos(episode_info, self.current_step)
                        self.callback.on_train_episode_info(envs=self.envs, policy=self.policy, env_id=i,
                                                            infos=info, rank=self.rank, use_wandb=self.use_wandb,
                                                            current_step=self.current_step,
                                                            current_episode=self.current_episode,
                                                            n_episodes=n_episodes)

                    obs_dict[i] = info[i]["reset_obs"]
                    envs.buf_obs[i] = info[i]["reset_obs"]
                    if self.use_actions_mask:
                        avail_actions[i] = info[i]["reset_avail_actions"]
                        envs.buf_avail_actions[i] = info[i]["reset_avail_actions"]
            _current_step += num_envs

        if test_mode:
            if self.config.render_mode == "rgb_array" and self.render:
                # time, height, width, channel -> time, channel, height, width
                videos_info = {"Videos_Test": np.array([episode_videos], dtype=np.uint8).transpose((0, 1, 4, 2, 3))}
                self.log_videos(info=videos_info, fps=self.fps, x_index=self.current_step)

            if self.config.test_mode:
                print("Best Score: %.2f" % best_score)

            test_info = {
                "Test-Results/Episode-Rewards/Mean-Score": np.mean(scores),
                "Test-Results/Episode-Rewards/Std-Score": np.std(scores),
            }
            self.log_infos(test_info, self.current_step)

            self.callback.on_test_end(envs=envs, policy=self.policy,
                                      current_train_step=self.current_step,
                                      current_step=_current_step, current_episode=_current_episode,
                                      scores=scores, best_score=best_score)

            if env_fn is not None:
                envs.close()
        return scores

    def train_epochs(self, n_epochs=1):
        """
        Train the model for numerous epochs.

        Returns:
            info_train (dict): The information of training.
        """
        info_train = {}
        if self.memory.full:
            indexes = np.arange(self.buffer_size)
            for _ in range(n_epochs):
                np.random.shuffle(indexes)
                for start in range(0, self.buffer_size, self.batch_size):
                    end = start + self.batch_size
                    sample_idx = indexes[start:end]
                    sample = self.memory.sample(sample_idx)
                    info_train = self.learner.update_rnn(sample) if self.use_rnn else self.learner.update(sample)
            self.callback.on_train_epochs_end(self.current_step, policy=self.policy, memory=self.memory,
                                              current_episode=self.current_episode, n_epochs=n_epochs,
                                              buffer_size=self.buffer_size, update_info=info_train)
            self.memory.clear()
        return info_train

    def test(self, env_fn, n_episodes):
        """
        Test the model for some episodes.

        Parameters:
            env_fn: The function that can make some testing environments.
            n_episodes (int): Number of episodes to test.

        Returns:
            scores (List(float)): A list of cumulative rewards for each episode.
        """
        scores = self.run_episodes(env_fn=env_fn, n_episodes=n_episodes, test_mode=True)
        return scores
