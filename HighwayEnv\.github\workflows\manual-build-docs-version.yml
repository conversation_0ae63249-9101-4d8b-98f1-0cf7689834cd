name: Manual Docs Versioning
on:
  workflow_dispatch:
    inputs:
      version:
          description: 'Documentation version to create'
          required: true
      commit:
          description: 'Commit used to build the Documentation version'
          required: false
      latest:
          description: 'Latest version'
          type: boolean

permissions:
  contents: write
env:
  OFFSCREEN_RENDERING: 1
jobs:
  docs:
    name: Generate Website for new version
    runs-on: ubuntu-latest
    env:
      SPHINX_GITHUB_CHANGELOG_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v3
        if: inputs.commit == ''

      - uses: actions/checkout@v3
        if: inputs.commit != ''
        with:
          ref: ${{ inputs.commit }}

      - uses: actions/setup-python@v4
        with:
            python-version: '3.9'

      - name: Install dependencies
        run: pip install -r docs/requirements.txt

      - name: Register Envs
        run: pip install -e .

      - name: Build
        run: sphinx-build -b dirhtml -v docs _build

      - name: Move 404
        run: mv _build/404/index.html _build/404.html

      - name: Update 404 links
        run: python docs/scripts/move_404.py _build/404.html

      - name: Remove .doctrees
        run: rm -r _build/.doctrees

      - name: Upload to GitHub Pages
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: _build
          target-folder: ${{ inputs.version }}
          clean: false

      - name: Upload to GitHub Pages
        uses: JamesIves/github-pages-deploy-action@v4
        if: inputs.latest
        with:
          folder: _build
          clean-exclude: |
            v*.*/
            main