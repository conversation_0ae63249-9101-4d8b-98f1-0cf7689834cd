#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的路径规划逻辑
验证横向偏移判断是否正确工作
"""

import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms/environments/single_agent_env'))

from custom_parking import SimpleHybridAStar

def test_path_planning_scenarios():
    """测试不同场景下的路径规划选择"""
    
    planner = SimpleHybridAStar()
    
    # 测试场景（更符合实际停车场景）
    test_cases = [
        {
            "name": "场景1: 理想直线停车（严格条件）",
            "start": (0, 0, 0),  # 车头朝向正东
            "goal": (5, 0.5, 0),  # 目标在前方很小偏移，朝向相同
            "expected": "forward_path"
        },
        {
            "name": "场景2: 小角度但偏移稍大（应选择弧线）",
            "start": (0, 0, 0),  # 车头朝向正东
            "goal": (8, 2, 0), # 目标在前方但偏移超过1.5m
            "expected": "arc_path"
        },
        {
            "name": "场景3: 目标在后方（应选择复杂路径）",
            "start": (0, 0, 0),   # 车头朝向正东
            "goal": (-5, 1, 0),   # 目标在后方
            "expected": "complex_path"
        },
        {
            "name": "场景4: 需要大角度调整（应选择复杂路径）",
            "start": (0, 0, 0),        # 车头朝向正东
            "goal": (3, 3, np.pi/2),   # 目标需要90度转向
            "expected": "arc_path"
        },
        {
            "name": "场景5: 完全对齐但距离较远（应选择弧线）",
            "start": (0, 0, 0),   # 车头朝向正东
            "goal": (15, 0, 0),   # 目标完全在前方但距离远
            "expected": "arc_path"  # 因为距离远，不满足严格条件
        },
        {
            "name": "场景6: 短距离大偏移（应选择复杂路径）",
            "start": (0, 0, 0),   # 车头朝向正东
            "goal": (4, 6, np.pi/4),   # 短距离但大偏移
            "expected": "complex_path"
        }
    ]
    
    print("=== 路径规划策略测试 ===\n")
    
    for i, case in enumerate(test_cases, 1):
        print(f"{i}. {case['name']}")
        print(f"   起点: {case['start']}")
        print(f"   终点: {case['goal']}")
        
        # 计算关键参数
        x1, y1, theta1 = case['start']
        x2, y2, theta2 = case['goal']
        
        # 计算角度差
        target_direction = np.arctan2(y2 - y1, x2 - x1)
        heading_diff = target_direction - theta1
        heading_diff = np.arctan2(np.sin(heading_diff), np.cos(heading_diff))
        
        # 计算横向偏移
        lateral_offset = abs((x2 - x1) * np.sin(theta1) - (y2 - y1) * np.cos(theta1))
        
        # 计算纵向距离
        longitudinal_distance = (x2 - x1) * np.cos(theta1) + (y2 - y1) * np.sin(theta1)
        
        print(f"   角度差: {np.degrees(heading_diff):.1f}°")
        print(f"   横向偏移: {lateral_offset:.2f}m")
        print(f"   纵向距离: {longitudinal_distance:.2f}m")
        
        # 计算最终朝向差异
        final_heading_diff = abs(theta2 - theta1)
        final_heading_diff = min(final_heading_diff, 2*np.pi - final_heading_diff)

        # 判断应该选择的路径类型（更严格的停车标准）
        if abs(heading_diff) < np.pi / 12:  # 角度差小于15度（更严格）
            if lateral_offset < 1.5 and longitudinal_distance > 2.0 and final_heading_diff < np.pi/6:
                predicted_path = "forward_path"
                print("   → 预测选择: 直线前进路径（严格条件）")
            else:
                predicted_path = "arc_path"
                print("   → 预测选择: 弧线路径（角度小但条件不满足）")
        elif abs(heading_diff) > 2 * np.pi / 3:
            predicted_path = "complex_path"
            print("   → 预测选择: 复杂机动路径（角度大）")
        else:
            distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
            if distance < 8.0 and lateral_offset > 5.0:
                predicted_path = "complex_path"
                print("   → 预测选择: 复杂机动路径（短距离大偏移）")
            else:
                predicted_path = "arc_path"
                print("   → 预测选择: 弧线路径（中等角度）")
        
        # 验证是否符合预期
        if predicted_path == case['expected']:
            print("   ✓ 符合预期")
        else:
            print(f"   ✗ 不符合预期（期望: {case['expected']}）")
        
        print()

def test_lateral_offset_calculation():
    """测试横向偏移计算的正确性"""
    print("=== 横向偏移计算验证 ===\n")
    
    # 测试用例：车头朝向正东（theta=0）
    theta1 = 0  # 朝向正东
    x1, y1 = 0, 0  # 起点在原点
    
    test_points = [
        (10, 0, "正前方"),
        (10, 2, "前方偏右2米"),
        (10, -3, "前方偏左3米"),
        (0, 5, "正右方5米"),
        (0, -4, "正左方4米"),
        (-5, 2, "后方偏右2米")
    ]
    
    for x2, y2, description in test_points:
        lateral_offset = abs((x2 - x1) * np.sin(theta1) - (y2 - y1) * np.cos(theta1))
        longitudinal_distance = (x2 - x1) * np.cos(theta1) + (y2 - y1) * np.sin(theta1)
        
        print(f"目标点: ({x2}, {y2}) - {description}")
        print(f"  横向偏移: {lateral_offset:.2f}m")
        print(f"  纵向距离: {longitudinal_distance:.2f}m")
        print()

if __name__ == "__main__":
    test_lateral_offset_calculation()
    test_path_planning_scenarios()
    
    print("测试完成！")
    print("\n改进说明:")
    print("1. 现在不仅考虑角度差，还考虑横向偏移")
    print("2. 只有当角度小且横向偏移小且目标在前方时，才选择直线前进")
    print("3. 这样可以避免在目标很远但角度小的情况下错误选择直线路径")
