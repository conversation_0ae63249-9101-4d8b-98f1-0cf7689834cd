#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的随机起始位置功能
验证地图是否恢复正常，只有车辆位置随机化
"""

import numpy as np
import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms/environments/single_agent_env'))

from custom_parking import CustomParkingEnv

class FixedSpawnConfig:
    """修复后的随机生成测试配置"""
    def __init__(self):
        # 基本环境配置
        self.env_seed = None  # 不固定种子
        self.max_episode_steps = 15
        self.render_mode = 'human'
        
        # 其他配置
        self.collision_reward = -10

def test_fixed_spawn():
    """测试修复后的随机起始位置"""
    print("=== 修复后的随机起始位置测试 ===\n")
    
    print("修复说明:")
    print("- 保持原始停车场地图不变")
    print("- 只修改车辆的初始位置")
    print("- 不添加额外的障碍物")
    print("- 保持停车位布局不变")
    print()
    
    config = FixedSpawnConfig()
    
    try:
        env = CustomParkingEnv(config)
        
        print("进行5次环境重置，观察地图和车辆位置...")
        
        for episode in range(5):
            print(f"\n--- Episode {episode + 1} ---")
            
            # 重置环境
            obs, info = env.reset()
            
            # 获取车辆信息
            if env.controlled_vehicles:
                vehicle = env.controlled_vehicles[0]
                x, y = vehicle.position
                heading = vehicle.heading
                
                print(f"车辆位置: ({x:6.2f}, {y:6.2f})")
                print(f"车辆朝向: {np.degrees(heading):6.1f}°")
                print(f"路径点数: {info.get('path_length', 0)}")
                
                # 渲染环境
                env.render()
                
                print("请检查:")
                print("1. 地图是否恢复正常（没有黄色方块）？")
                print("2. 停车位是否正常显示？")
                print("3. 车辆位置是否随机？")
                print("4. 路径规划是否正常？")
                
                # 等待用户观察
                time.sleep(2)
            else:
                print("无法获取车辆信息")
        
        env.close()
        
        print("\n测试完成！")
        
        # 获取用户反馈
        feedback = input("地图是否恢复正常？(y/n): ").lower().strip()
        
        if feedback in ['y', 'yes', '是']:
            print("✅ 太好了！修复成功，地图恢复正常。")
            print("现在只有车辆位置是随机的，地图结构保持不变。")
        else:
            print("❌ 还有问题，可能需要进一步调整。")
            
    except KeyboardInterrupt:
        print("用户中断测试")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

def compare_with_original():
    """与原始环境对比"""
    print("\n=== 与原始环境对比 ===\n")
    
    print("修改前的问题:")
    print("- 重写了整个_create_vehicles方法")
    print("- 意外添加了额外的障碍物")
    print("- 改变了地图的基本结构")
    print("- 出现了不应该存在的黄色方块")
    print()
    
    print("修改后的改进:")
    print("- 先调用父类方法保持原有地图")
    print("- 只修改控制车辆的初始位置")
    print("- 不添加任何额外的地图元素")
    print("- 保持停车场的原始布局")
    print()
    
    print("实现方式:")
    print("```python")
    print("def _create_vehicles(self):")
    print("    # 先调用父类方法创建基础地图")
    print("    super()._create_vehicles()")
    print("    ")
    print("    # 然后只修改车辆位置")
    print("    for vehicle in self.controlled_vehicles:")
    print("        x0 = self.np_random.uniform(-25, 25)")
    print("        vehicle.position = [x0, 0]")
    print("        vehicle.heading = 2 * np.pi * self.np_random.uniform()")
    print("```")

if __name__ == "__main__":
    compare_with_original()
    print("\n" + "="*50 + "\n")
    
    # 询问是否运行测试
    response = input("是否运行修复测试？(y/n): ").lower().strip()
    
    if response in ['y', 'yes', '是']:
        test_fixed_spawn()
    else:
        print("测试已取消")
        print("\n现在的实现应该只会随机化车辆位置，不会改变地图结构。")
