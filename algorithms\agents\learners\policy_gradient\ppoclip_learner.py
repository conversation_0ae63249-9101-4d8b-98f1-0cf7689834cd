# ================================================================================================
# PPO-CLIP 学习器实现
#
# PPO (Proximal Policy Optimization) 是一种策略梯度算法，通过裁剪机制限制策略更新幅度
#
# 核心思想:
# 1. 使用重要性采样比率 (importance sampling ratio) 来重用旧数据
# 2. 通过裁剪机制防止策略更新过大，避免性能崩溃
# 3. 结合 Actor-Critic 架构，同时优化策略和价值函数
#
# 论文链接: https://arxiv.org/pdf/1707.06347.pdf
# 实现框架: PyTorch
# ================================================================================================

import torch                                        # PyTorch 深度学习框架
from torch import nn                               # 神经网络模块
from algorithms.agents.learners import Learner    # 学习器基类
from argparse import Namespace                     # 命名空间类，用于配置管理


class PPOCLIP_Learner(Learner):
    """
    PPO-CLIP 学习器类

    实现了PPO算法的核心学习逻辑，包括：
    1. 策略损失计算（带裁剪的重要性采样）
    2. 价值函数损失计算
    3. 熵正则化
    4. 梯度更新和学习率调度

    继承自 Learner 基类，专门用于策略梯度算法的参数更新。
    """
    def __init__(self,
                 config: Namespace,
                 policy: nn.Module,
                 callback):
        """
        初始化 PPO-CLIP 学习器

        Args:
            config (Namespace): 配置参数，包含学习率、裁剪范围等超参数
            policy (nn.Module): 策略网络（Actor-Critic网络）
            callback: 回调函数，用于训练过程中的自定义逻辑
        """
        # 调用父类初始化方法
        super(PPOCLIP_Learner, self).__init__(config, policy, callback)

        # 初始化Adam优化器，用于策略网络参数更新
        self.optimizer = torch.optim.Adam(self.policy.parameters(), self.config.learning_rate, eps=1e-5)

        # 初始化线性学习率调度器，实现学习率的线性衰减
        # start_factor=1.0: 初始学习率保持不变
        # end_factor: 最终学习率衰减因子
        # total_iters: 总的训练迭代次数
        self.scheduler = torch.optim.lr_scheduler.LinearLR(self.optimizer,
                                                           start_factor=1.0,
                                                           end_factor=self.end_factor_lr_decay,
                                                           total_iters=self.total_iters)

        # 初始化均方误差损失函数，用于价值函数的损失计算
        self.mse_loss = nn.MSELoss()

        # PPO算法的关键超参数
        self.vf_coef = config.vf_coef        # 价值函数损失的权重系数
        self.ent_coef = config.ent_coef      # 熵正则化的权重系数
        self.clip_range = config.clip_range  # 重要性采样比率的裁剪范围

    def estimate_total_iterations(self):
        """
        估算总的训练迭代次数

        用于学习率调度器的初始化，计算整个训练过程中的总更新次数。
        这个估算对于学习率的线性衰减调度非常重要。

        Returns:
            int: 估算的总迭代次数
        """
        # 计算经验缓冲区大小 = 时间步长 × 并行环境数
        buffer_size = self.config.horizon_size * self.config.parallels

        # 计算总的更新轮数 = 总训练步数 ÷ 缓冲区大小
        update_times = self.config.running_steps // buffer_size

        # 计算总迭代次数 = 更新轮数 × 每轮epoch数 × 每epoch的minibatch数
        total_iters = update_times * self.config.n_epochs * self.config.n_minibatch

        return total_iters

    def update(self, **samples):
        """
        PPO-CLIP 算法的核心更新函数

        执行一次策略网络的参数更新，包括：
        1. 数据预处理和转换
        2. 前向传播计算
        3. PPO损失计算（裁剪重要性采样）
        4. 反向传播和参数更新
        5. 学习率调度和日志记录

        Args:
            **samples: 包含训练数据的字典，包括观察、动作、回报、优势等

        Returns:
            dict: 包含损失值和其他训练统计信息的字典
        """
        # ========================================================================================
        # 步骤1: 数据预处理
        # ========================================================================================

        # 增加迭代计数器
        self.iterations += 1

        # 将numpy数组转换为PyTorch张量并移动到指定设备（CPU/GPU）
        obs_batch = torch.as_tensor(samples['obs'], device=self.device)              # 观察批次
        act_batch = torch.as_tensor(samples['actions'], device=self.device)         # 动作批次
        ret_batch = torch.as_tensor(samples['returns'], device=self.device)         # 回报批次（目标值）
        adv_batch = torch.as_tensor(samples['advantages'], device=self.device)      # 优势函数批次
        old_logp_batch = torch.as_tensor(samples['aux_batch']['old_logp'], device=self.device)  # 旧策略的对数概率

        # 调用更新开始回调函数，允许用户自定义逻辑
        info = self.callback.on_update_start(self.iterations,
                                             policy=self.policy, obs=obs_batch, act=act_batch,
                                             returns=ret_batch, advantages=adv_batch, old_logp=old_logp_batch)

        # ========================================================================================
        # 步骤2: 前向传播
        # ========================================================================================

        # 通过策略网络进行前向传播，获取输出、动作分布和价值预测
        outputs, a_dist, v_pred = self.policy(obs_batch)

        # 计算当前策略下动作的对数概率
        log_prob = a_dist.log_prob(act_batch)

        # ========================================================================================
        # 步骤3: PPO-CLIP 核心算法实现
        # ========================================================================================

        # 计算重要性采样比率 = exp(log π_new - log π_old) = π_new / π_old
        # 这个比率衡量新旧策略在选择相同动作时的概率差异
        ratio = (log_prob - old_logp_batch).exp().float()

        # PPO的核心创新：裁剪重要性采样比率
        # surrogate1: 裁剪后的目标函数，限制比率在 [1-ε, 1+ε] 范围内
        surrogate1 = ratio.clamp(1.0 - self.clip_range, 1.0 + self.clip_range) * adv_batch

        # surrogate2: 未裁剪的目标函数
        surrogate2 = adv_batch * ratio

        # 策略损失：取两个目标函数的最小值，这是PPO的关键机制
        # 负号是因为我们要最大化目标函数，但优化器执行最小化
        a_loss = -torch.minimum(surrogate1, surrogate2).mean()

        # ========================================================================================
        # 步骤4: 价值函数和熵损失计算
        # ========================================================================================

        # 价值函数损失：预测价值与实际回报之间的均方误差
        # detach() 防止梯度回传到回报计算
        c_loss = self.mse_loss(v_pred, ret_batch.detach())

        # 熵损失：鼓励策略保持一定的随机性，避免过早收敛到确定性策略
        e_loss = a_dist.entropy().mean()

        # 总损失 = 策略损失 - 熵正则化项 + 价值函数损失
        # 注意：熵项前面是负号，因为我们要最大化熵（增加探索）
        loss = a_loss - self.ent_coef * e_loss + self.vf_coef * c_loss
        # ========================================================================================
        # 步骤5: 反向传播和参数更新
        # ========================================================================================

        # 清零梯度，防止梯度累积
        self.optimizer.zero_grad()

        # 反向传播计算梯度
        loss.backward()

        # 梯度裁剪（如果启用）：防止梯度爆炸，提高训练稳定性
        if self.use_grad_clip:
            torch.nn.utils.clip_grad_norm_(self.policy.parameters(), self.grad_clip_norm)

        # 执行参数更新
        self.optimizer.step()

        # 更新学习率调度器（如果存在）
        if self.scheduler is not None:
            self.scheduler.step()

        # ========================================================================================
        # 步骤6: 统计信息计算和日志记录
        # ========================================================================================

        # 获取当前学习率
        lr = self.optimizer.state_dict()['param_groups'][0]['lr']

        # 计算裁剪比率：被裁剪的样本占总样本的比例
        # 这个指标反映了策略更新的激进程度
        cr = ((ratio < 1 - self.clip_range).sum() + (ratio > 1 + self.clip_range).sum()) / ratio.shape[0]
        
        # 根据是否使用分布式训练来组织日志信息
        if self.distributed_training:
            # 分布式训练：为每个进程添加rank标识
            info.update({
                f"actor_loss/rank_{self.rank}": a_loss.item(),           # 策略损失
                f"critic_loss/rank_{self.rank}": c_loss.item(),          # 价值函数损失
                f"entropy/rank_{self.rank}": e_loss.item(),              # 熵值
                f"learning_rate/rank_{self.rank}": lr,                   # 当前学习率
                f"predict_value/rank_{self.rank}": v_pred.mean().item(), # 预测价值的平均值
                f"clip_ratio/rank_{self.rank}": cr                       # 裁剪比率
            })
        else:
            # 单机训练：直接记录指标
            info.update({
                "actor_loss": a_loss.item(),           # 策略损失（Actor损失）
                "critic_loss": c_loss.item(),          # 价值函数损失（Critic损失）
                "entropy": e_loss.item(),              # 策略熵值（探索程度指标）
                "learning_rate": lr,                   # 当前学习率
                "predict_value": v_pred.mean().item(), # 预测价值的平均值
                "clip_ratio": cr                       # 裁剪比率（策略更新激进程度）
            })

        # 调用更新结束回调函数，传递详细的训练信息
        # 这允许用户监控和分析训练过程中的各种中间变量
        info.update(self.callback.on_update_end(self.iterations,
                                                policy=self.policy, info=info, rep_output=outputs,
                                                a_dist=a_dist, v_pred=v_pred, log_prob=log_prob,
                                                ratio=ratio, surrogate1=surrogate1, surrogate2=surrogate2,
                                                a_loss=a_loss, c_loss=c_loss, e_loss=e_loss, loss=loss))

        # 返回包含所有训练统计信息的字典
        return info
