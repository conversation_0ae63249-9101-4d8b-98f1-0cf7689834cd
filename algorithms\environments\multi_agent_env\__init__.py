from algorithms.environments.utils import EnvironmentDict
from algorithms.utils import Optional

REGISTRY_MULTI_AGENT_ENV: Optional[EnvironmentDict] = {}

try:
    from algorithms.environments.multi_agent_env.highway_ma import Highway_MultiAgent_Env
    REGISTRY_MULTI_AGENT_ENV['highway_ma'] = Highway_MultiAgent_Env
except Exception as error:
    REGISTRY_MULTI_AGENT_ENV["highway_ma"] = str(error)

__all__ = [
    "REGISTRY_MULTI_AGENT_ENV",
]