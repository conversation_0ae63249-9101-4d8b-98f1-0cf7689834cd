# ================================================================================================
# 在策略（On-Policy）强化学习智能体核心基类
#
# 这个文件实现了在策略强化学习算法的基础框架，主要用于：
# 1. PPO (Proximal Policy Optimization) 算法
# 2. A2C (Advantage Actor-Critic) 算法
# 3. 其他基于策略梯度的在策略算法
#
# 在策略算法的特点：
# - 使用当前策略收集的数据来更新策略
# - 数据收集和策略更新是同步进行的
# - 相比离策略算法，样本效率较低但更稳定
# ================================================================================================

import numpy as np                                    # 数值计算库
from tqdm import tqdm                                # 进度条显示库
from copy import deepcopy                            # 深拷贝函数
from argparse import Namespace                       # 命名空间类，用于配置管理
from algorithms.utils import Optional, Union, DummyOnPolicyBuffer, DummyOnPolicyBuffer_Atari  # 缓冲区和类型注解
from algorithms.environments import DummyVecEnv, SubprocVecEnv  # 向量化环境
from algorithms.agents import Module                 # 神经网络模块基类
from algorithms.agents.utils import split_distributions  # 分布处理工具
from algorithms.agents.agents.base import Agent, BaseCallback  # 智能体基类和回调函数


class OnPolicyAgent(Agent):
    """
    在策略强化学习智能体的核心基类

    这是所有在策略算法（如PPO、A2C）的基础类，提供了：
    1. 经验缓冲区管理
    2. 数据收集和处理流程
    3. 策略更新的基础框架
    4. GAE（广义优势估计）计算

    在策略算法的工作流程：
    1. 使用当前策略收集经验数据
    2. 计算优势函数和回报
    3. 使用收集的数据更新策略
    4. 清空缓冲区，重复上述过程

    Args:
        config (Namespace): 配置参数，包含超参数和其他设置
        envs (Union[DummyVecEnv, SubprocVecEnv]): 向量化环境，支持并行环境
        callback (Optional[BaseCallback]): 用户自定义回调函数，用于：
            - 日志记录和可视化
            - 早停和模型保存
            - 自定义训练逻辑注入
            - 如果未提供，使用默认的空操作回调
    """
    def __init__(self,
                 config: Namespace,
                 envs: Union[DummyVecEnv, SubprocVecEnv],
                 callback: Optional[BaseCallback] = None):
        """
        初始化在策略智能体

        设置在策略算法所需的核心参数和组件
        """
        # 调用父类初始化方法
        super(OnPolicyAgent, self).__init__(config, envs, callback)

        # ========================================================================================
        # 在策略算法的核心参数
        # ========================================================================================

        # 时间视野大小：每次收集多少步的经验数据
        self.horizon_size = config.horizon_size

        # 训练轮数：每批数据要训练多少个epoch
        self.n_epochs = config.n_epochs

        # 小批次数量：将缓冲区数据分成多少个mini-batch
        self.n_minibatch = config.n_minibatch

        # GAE lambda参数：用于广义优势估计的衰减因子
        self.gae_lam = config.gae_lambda

        # 辅助信息形状：用于存储额外的网络输出信息
        self.auxiliary_info_shape = None

        # 经验缓冲区：存储收集的经验数据
        self.memory: Optional[DummyOnPolicyBuffer] = None

    def _build_memory(self, auxiliary_info_shape=None):
        """
        构建经验缓冲区

        根据环境类型和配置参数创建合适的经验缓冲区，用于存储智能体与环境交互的数据。

        Args:
            auxiliary_info_shape: 辅助信息的形状，用于存储额外的网络输出

        Returns:
            DummyOnPolicyBuffer: 配置好的经验缓冲区实例
        """
        # 检查是否为Atari环境，Atari环境需要特殊的缓冲区处理
        self.atari = self.config.env_name == "Atari"

        # 根据环境类型选择合适的缓冲区类
        Buffer = DummyOnPolicyBuffer_Atari if self.atari else DummyOnPolicyBuffer

        # 计算缓冲区大小 = 环境数量 × 时间视野大小
        self.buffer_size = self.n_envs * self.horizon_size

        # 计算批次大小 = 缓冲区大小 ÷ 小批次数量
        self.batch_size = self.buffer_size // self.n_minibatch

        # 构建缓冲区初始化参数
        input_buffer = dict(
            observation_space=self.observation_space,    # 观察空间
            action_space=self.action_space,              # 动作空间
            auxiliary_shape=auxiliary_info_shape,        # 辅助信息形状
            n_envs=self.n_envs,                         # 环境数量
            horizon_size=self.horizon_size,             # 时间视野大小
            use_gae=self.config.use_gae,                # 是否使用GAE
            use_advnorm=self.config.use_advnorm,        # 是否使用优势归一化
            gamma=self.gamma,                           # 折扣因子
            gae_lam=self.gae_lam                        # GAE lambda参数
        )

        # 创建并返回缓冲区实例
        return Buffer(**input_buffer)

    def _build_policy(self) -> Module:
        """
        构建策略网络

        这是一个抽象方法，需要在子类中实现具体的策略网络构建逻辑。
        不同的算法（如PPO、A2C）会有不同的网络结构。

        Returns:
            Module: 策略网络模块
        """
        raise NotImplementedError

    def get_terminated_values(self, observations_next: np.ndarray, rewards: np.ndarray = None) -> np.ndarray:
        """
        获取终止状态的价值估计

        当环境回合结束时，需要计算最后一个状态的价值，用于GAE计算。
        这对于准确估计优势函数非常重要。

        Args:
            observations_next (np.ndarray): 终止状态的观察
            rewards (np.ndarray): 终止状态的奖励（可选）

        Returns:
            np.ndarray: 终止状态的价值估计
        """
        # 处理观察并通过策略网络获取价值估计
        policy_out = self.action(self._process_observation(observations_next))
        values_next = policy_out['values']
        return values_next

    def action(self, observations: np.ndarray,
               return_dists: bool = False, return_logpi: bool = False) -> dict:
        """
        根据观察选择动作

        这是在策略算法的核心方法，根据当前策略和观察选择动作。
        同时返回状态价值估计，用于后续的优势函数计算。

        Args:
            observations (np.ndarray): 环境观察，形状为 [batch_size, obs_dim]
            return_dists (bool): 是否返回策略分布，用于后续分析
            return_logpi (bool): 是否返回动作的对数概率，用于重要性采样

        Returns:
            dict: 包含以下键值的字典：
                - actions: 要执行的动作
                - values: 状态价值估计V(s)
                - dists: 策略分布（如果requested）
                - log_pi: 动作的对数概率（如果requested）
        """
        # 通过策略网络获取输出、分布和价值
        _, policy_dists, values = self.policy(observations)

        # 从策略分布中随机采样动作（探索）
        actions = policy_dists.stochastic_sample()

        # 计算动作的对数概率（如果需要）
        log_pi = policy_dists.log_prob(actions).detach().cpu().numpy() if return_logpi else None

        # 分割分布信息（如果需要）
        dists = split_distributions(policy_dists) if return_dists else None

        # 将动作转换为numpy数组
        actions = actions.detach().cpu().numpy()

        # 处理价值估计
        if values is None:
            values = 0
        else:
            values = values.detach().cpu().numpy()

        return {"actions": actions, "values": values, "dists": dists, "log_pi": log_pi}

    def get_aux_info(self, policy_output: dict = None) -> dict:
        """
        获取辅助信息

        返回额外的策略输出信息，可以在子类中重写以提供特定算法需要的信息。

        Args:
            policy_output (dict): 策略网络的输出信息

        Returns:
            dict: 辅助信息字典，默认为空
        """
        return {}

    def train_epochs(self, n_epochs: int = 1) -> dict:
        """
        执行多个训练轮次

        这是在策略算法的核心训练循环，将缓冲区中的数据分成小批次，
        进行多轮训练以充分利用收集的经验数据。

        Args:
            n_epochs (int): 训练轮数，默认为1

        Returns:
            dict: 训练信息，包含损失值等统计数据
        """
        # 创建数据索引数组
        indexes = np.arange(self.buffer_size)
        train_info = {}

        # 执行多轮训练
        for _ in range(n_epochs):
            # 随机打乱数据顺序，增加训练的随机性
            np.random.shuffle(indexes)

            # 按小批次处理数据
            for start in range(0, self.buffer_size, self.batch_size):
                end = start + self.batch_size
                sample_idx = indexes[start:end]

                # 从缓冲区采样数据
                samples = self.memory.sample(sample_idx)

                # 使用学习器更新网络参数
                train_info = self.learner.update(**samples)

        return train_info

    def train(self, train_steps: int) -> dict:
        """
        在策略算法的主训练循环

        这是整个在策略强化学习的核心方法，实现了完整的训练流程：
        1. 数据收集：使用当前策略与环境交互收集经验
        2. 缓冲区管理：存储经验数据并计算优势函数
        3. 策略更新：使用收集的数据更新策略网络
        4. 循环重复：清空缓冲区，开始下一轮收集

        Args:
            train_steps (int): 总的训练步数（实际是更新轮数）

        Returns:
            dict: 训练信息统计
        """
        train_info = {}

        # 获取初始观察
        obs = self.envs.buf_obs

        # 主训练循环，显示进度条
        for _ in tqdm(range(train_steps)):
            # ========================================================================================
            # 阶段1: 数据收集
            # ========================================================================================

            # 更新观察归一化统计
            self.obs_rms.update(obs)

            # 处理观察（归一化等）
            obs = self._process_observation(obs)

            # 根据当前策略选择动作
            policy_out = self.action(obs, return_dists=False, return_logpi=False)
            acts, vals = policy_out['actions'], policy_out['values']

            # 在环境中执行动作
            next_obs, rewards, terminals, truncations, infos = self.envs.step(acts)

            # 获取辅助信息
            aux_info = self.get_aux_info()

            # 调用训练步骤回调函数
            self.callback.on_train_step(self.current_step, envs=self.envs, policy=self.policy,
                                        obs=obs, policy_out=policy_out, acts=acts, vals=vals, next_obs=next_obs,
                                        rewards=rewards, terminals=terminals, truncations=truncations,
                                        infos=infos, aux_info=aux_info, train_steps=train_steps)

            # ========================================================================================
            # 阶段2: 经验存储和缓冲区管理
            # ========================================================================================

            # 将经验存储到缓冲区
            self.memory.store(obs, acts, self._process_reward(rewards), vals, terminals, aux_info)

            # 当缓冲区满时，进行策略更新
            if self.memory.full:
                # 计算终止状态的价值估计
                vals = self.get_terminated_values(next_obs, rewards)

                # 为每个环境完成轨迹路径
                for i in range(self.n_envs):
                    if terminals[i]:
                        # 终止状态的价值为0
                        self.memory.finish_path(0.0, i)
                    else:
                        # 非终止状态使用价值网络的估计
                        self.memory.finish_path(vals[i], i)

                # ========================================================================================
                # 阶段3: 策略更新
                # ========================================================================================

                # 执行多轮训练更新
                update_info = self.train_epochs(self.n_epochs)

                # 记录训练信息
                self.log_infos(update_info, self.current_step)
                train_info.update(update_info)

                # 调用训练轮次结束回调
                self.callback.on_train_epochs_end(self.current_step, policy=self.policy, memory=self.memory,
                                                  current_episode=self.current_episode, train_steps=train_steps,
                                                  update_info=update_info)

                # 清空缓冲区，准备下一轮数据收集
                self.memory.clear()

            self.returns = self.gamma * self.returns + rewards
            obs = deepcopy(next_obs)
            for i in range(self.n_envs):
                if terminals[i] or truncations[i]:
                    self.ret_rms.update(self.returns[i:i + 1])
                    self.returns[i] = 0.0
                    if self.atari and (~truncations[i]):
                        pass
                    else:
                        if terminals[i]:
                            self.memory.finish_path(0, i)
                        else:
                            vals = self.get_terminated_values(next_obs, rewards)
                            self.memory.finish_path(vals[i], i)
                        obs[i] = infos[i]["reset_obs"]
                        self.envs.buf_obs[i] = obs[i]
                        self.current_episode[i] += 1
                        if self.use_wandb:
                            episode_info = {
                                f"Episode-Steps/rank_{self.rank}/env-{i}": infos[i]["episode_step"],
                                f"Train-Episode-Rewards/rank_{self.rank}/env-{i}": infos[i]["episode_score"]
                            }
                        else:
                            episode_info = {
                                f"Episode-Steps/rank_{self.rank}": {f"env-{i}": infos[i]["episode_step"]},
                                f"Train-Episode-Rewards/rank_{self.rank}": {f"env-{i}": infos[i]["episode_score"]}
                            }
                        self.log_infos(episode_info, self.current_step)
                        train_info.update(episode_info)
                        self.callback.on_train_episode_info(envs=self.envs, policy=self.policy, env_id=i,
                                                            infos=infos, rank=self.rank, use_wandb=self.use_wandb,
                                                            current_step=self.current_step,
                                                            current_episode=self.current_episode,
                                                            train_steps=train_steps)

            self.current_step += self.n_envs
            self.callback.on_train_step_end(self.current_step, envs=self.envs, policy=self.policy,
                                            train_steps=train_steps, train_info=train_info)
        return train_info

    def test(self, env_fn, test_episodes: int) -> list:
        """
        测试训练好的策略性能

        使用当前训练的策略在测试环境中运行指定数量的回合，评估策略的表现。
        这个方法不会更新策略参数，只是用来评估当前策略的性能。

        主要功能：
        1. 策略评估：使用当前策略在测试环境中执行动作
        2. 性能统计：记录每个回合的得分和统计信息
        3. 视频录制：如果启用渲染，会录制最佳表现的视频
        4. 日志记录：记录测试结果到日志系统

        Args:
            env_fn: 环境创建函数，用于创建测试环境
            test_episodes (int): 要测试的回合数

        Returns:
            list: 每个测试回合的得分列表

        测试流程：
            1. 创建测试环境并重置
            2. 使用当前策略执行动作
            3. 记录每个回合的得分
            4. 统计平均得分和标准差
            5. 保存最佳表现的视频（如果启用）
        """
        # 创建测试环境
        test_envs = env_fn()
        num_envs = test_envs.num_envs

        # 初始化测试相关变量
        videos, episode_videos, images = [[] for _ in range(num_envs)], [], None
        current_episode, current_step, scores, best_score = 0, 0, [], -np.inf

        # 重置环境获取初始观察
        obs, infos = test_envs.reset()
        # 如果启用视频录制，开始记录初始帧
        if self.config.render_mode == "rgb_array" and self.render:
            images = test_envs.render(self.config.render_mode)
            for idx, img in enumerate(images):
                videos[idx].append(img)

        # 主测试循环：运行指定数量的测试回合
        while current_episode < test_episodes:
            # 更新观察统计信息（用于归一化）
            self.obs_rms.update(obs)

            # 预处理观察（归一化等）
            obs = self._process_observation(obs)

            # 使用当前策略选择动作（不进行探索，使用确定性策略）
            policy_out = self.action(obs)

            # 在环境中执行动作
            next_obs, rewards, terminals, truncations, infos = test_envs.step(policy_out['actions'])

            # 如果启用视频录制，记录当前帧
            if self.config.render_mode == "rgb_array" and self.render:
                images = test_envs.render(self.config.render_mode)
                for idx, img in enumerate(images):
                    videos[idx].append(img)

            # 调用测试步骤回调函数，允许用户自定义测试逻辑
            self.callback.on_test_step(envs=test_envs, policy=self.policy, images=images,
                                       obs=obs, policy_out=policy_out, next_obs=next_obs, rewards=rewards,
                                       terminals=terminals, truncations=truncations, infos=infos,
                                       current_train_step=self.current_step,
                                       current_step=current_step, current_episode=current_episode)

            # 更新观察状态
            obs = deepcopy(next_obs)

            # 检查每个环境是否完成回合
            for i in range(num_envs):
                if terminals[i] or truncations[i]:
                    # Atari游戏的特殊处理：区分真正的终止和时间截断
                    if self.atari and (~truncations[i]):
                        pass  # Atari游戏中，只有真正的游戏结束才处理
                    else:
                        # 重置环境并记录回合得分
                        obs[i] = infos[i]["reset_obs"]
                        scores.append(infos[i]["episode_score"])
                        current_episode += 1

                        # 更新最佳得分和对应的视频
                        if best_score < infos[i]["episode_score"]:
                            best_score = infos[i]["episode_score"]
                            episode_videos = videos[i].copy()

                        # 如果启用测试模式，打印当前回合得分
                        if self.config.test_mode:
                            print("Episode: %d, Score: %.2f" % (current_episode, infos[i]["episode_score"]))

            # 更新步数计数器
            current_step += num_envs

        # 保存最佳表现的测试视频
        if self.config.render_mode == "rgb_array" and self.render:
            # 转换视频格式：时间×高度×宽度×通道 -> 时间×通道×高度×宽度
            videos_info = {"Videos_Test": np.array([episode_videos], dtype=np.uint8).transpose((0, 1, 4, 2, 3))}
            self.log_videos(info=videos_info, fps=self.fps, x_index=self.current_step)

        # 如果启用测试模式，打印最佳得分
        if self.config.test_mode:
            print("Best Score: %.2f" % (best_score))

        # 计算测试统计信息
        test_info = {
            "Test-Episode-Rewards/Mean-Score": np.mean(scores),    # 平均得分
            "Test-Episode-Rewards/Std-Score": np.std(scores)       # 得分标准差
        }

        # 记录测试信息到日志系统
        self.log_infos(test_info, self.current_step)

        # 调用测试结束回调函数
        self.callback.on_test_end(envs=test_envs, policy=self.policy,
                                  current_train_step=self.current_step,
                                  current_step=current_step, current_episode=current_episode,
                                  scores=scores, best_score=best_score)

        # 关闭测试环境，释放资源
        test_envs.close()

        # 返回所有测试回合的得分列表
        return scores
