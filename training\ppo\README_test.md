# PPO停车环境测试说明

## 文件说明

### 1. test_ppo_parking.py
PPO停车环境的测试脚本，用于评估已训练好的模型性能。

### 2. test_ppo_parking.yaml
测试专用的配置文件，包含测试相关的参数设置。

## 使用方法

### 基本测试
```bash
# 使用默认配置进行测试
python test_ppo_parking.py

# 指定测试回合数
python test_ppo_parking.py --test-episodes 20

# 启用渲染观察智能体行为
python test_ppo_parking.py --render 1

# 指定模型路径
python test_ppo_parking.py --model-path "./result/train/models/"
```

### 参数说明

- `--env-id`: 环境ID，默认为 "parking-v0"
- `--model-path`: 模型文件路径，如果不指定则使用配置文件中的路径
- `--test-episodes`: 测试回合数，如果不指定则使用配置文件中的设置
- `--render`: 是否渲染 (0: 否, 1: 是)
- `--save-results`: 是否保存测试结果 (0: 否, 1: 是)
- `--verbose`: 是否显示详细信息 (0: 否, 1: 是)

## 测试结果

测试完成后会显示以下统计信息：
- 平均分数和标准差
- 最高分数和最低分数
- 成功率
- 平均回合长度
- 分数分布

## 结果保存

如果启用了结果保存（默认启用），测试结果会保存到：
- `./result/test/results/test_results_YYYYMMDD_HHMMSS.json` - 详细结果
- `./result/test/results/test_summary_YYYYMMDD_HHMMSS.txt` - 结果摘要

## 配置文件说明

`test_ppo_parking.yaml` 包含以下主要配置：

### 渲染配置
- `render: True` - 测试时启用渲染
- `render_mode: 'human'` - 实时显示模式
- `fps: 30` - 渲染帧率

### 测试参数
- `test_episode: 10` - 测试回合数
- `parallels: 1` - 测试时使用单个环境

### 模型加载
- `model_dir_load: "./result/train/models/"` - 模型路径
- `model_name_load: "best_model.pth"` - 模型文件名

### 结果保存
- `save_test_results: True` - 保存测试结果
- `test_results_dir: "./result/test/results/"` - 结果保存目录

## 注意事项

1. 确保已完成训练并且模型文件存在
2. 测试时建议启用渲染以观察智能体行为
3. 可以通过调整 `test_episode` 参数来获得更稳定的测试结果
4. 测试结果会自动保存，便于后续分析和比较

## 性能评估标准

- **优秀**: 成功率 > 80%
- **良好**: 成功率 > 60%
- **一般**: 成功率 > 40%
- **较差**: 成功率 ≤ 40%

成功的判断标准是回合分数大于 0.0（可在代码中调整）。
