# ================================================================================================
# MLP（多层感知机）表征网络实现
#
# 这个文件实现了基于多层感知机的表征网络，用于处理向量化的观察输入：
# 1. Basic_Identical - 恒等映射，直接返回原始观察
# 2. Basic_MLP - 标准的多层感知机，进行特征提取和变换
#
# MLP适用场景：
# - 低维向量输入（如机器人关节角度、游戏状态等）
# - 不需要空间或时序建模的任务
# - 计算效率要求较高的场景
# ================================================================================================

import numpy as np                                    # 数值计算库
from algorithms.utils import Sequence, Optional, Union, Callable  # 类型注解
from algorithms.agents import Module, Tensor         # 神经网络模块基类
import torch                                         # PyTorch深度学习框架
from torch import nn                                 # PyTorch神经网络模块
from algorithms.agents.utils import mlp_block, ModuleType  # MLP构建工具


class Basic_Identical(Module):
    """
    恒等表征网络

    这是最简单的表征网络，直接返回原始观察而不进行任何变换。
    适用于观察已经是良好特征表示的情况，或者作为基线对比。

    使用场景：
    - 观察已经经过预处理，不需要进一步特征提取
    - 作为消融实验的基线
    - 调试时验证其他组件的正确性

    Args:
        input_shape (Sequence[int]): 输入观察的形状，必须是1维
        device (Optional[Union[str, int, torch.device]]): 计算设备
        **kwargs: 其他参数（为了接口一致性）
    """

    def __init__(self,
                 input_shape: Sequence[int],
                 device: Optional[Union[str, int, torch.device]] = None,
                 **kwargs):
        """
        初始化恒等表征网络

        Args:
            input_shape: 输入形状，必须是1维向量
            device: 计算设备（CPU或GPU）
        """
        super(Basic_Identical, self).__init__()

        # 确保输入是1维向量
        assert len(input_shape) == 1, "恒等网络只支持1维输入"

        # 设置输出形状（与输入相同）
        self.output_shapes = {'state': (input_shape[0],)}
        self.device = device

    def forward(self, observations: np.ndarray):
        """
        前向传播：直接返回输入观察

        Args:
            observations (np.ndarray): 输入观察数据

        Returns:
            dict: 包含'state'键的字典，值为转换后的张量
        """
        # 将numpy数组转换为PyTorch张量，不进行任何变换
        state = torch.as_tensor(observations, dtype=torch.float32, device=self.device)
        return {'state': state}


class Basic_MLP(Module):
    """
    基础多层感知机表征网络

    这是标准的MLP网络，通过多个全连接层对输入观察进行特征提取和变换。
    是强化学习中最常用的表征网络，适用于大多数向量化输入的任务。

    网络结构：
    输入层 -> 隐藏层1 -> 激活函数 -> ... -> 隐藏层N -> 激活函数 -> 输出层

    使用场景：
    - 低维向量输入（如你的停车任务的6维观察）
    - 不需要处理图像或序列数据的任务
    - 需要非线性特征变换的场景

    你的停车任务中：
    输入(6维) -> 隐藏层(256) -> ReLU -> 输出(256维特征)

    Args:
        input_shape (Sequence[int]): 输入观察的形状
        hidden_sizes (Sequence[int]): 各隐藏层的神经元数量，如[256, 128]
        normalize (Optional[ModuleType]): 归一化层类型（如BatchNorm）
        initialize (Optional[Callable]): 权重初始化方法
        activation (Optional[ModuleType]): 激活函数类型（如ReLU）
        device (Optional[Union[str, int, torch.device]]): 计算设备
    """

    def __init__(self,
                 input_shape: Sequence[int],
                 hidden_sizes: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None,
                 **kwargs):
        """
        初始化MLP表征网络

        Args:
            input_shape: 输入形状，如(6,)表示6维向量
            hidden_sizes: 隐藏层大小列表，如[256]表示一个256神经元的隐藏层
            normalize: 归一化方法（可选）
            initialize: 权重初始化方法（可选）
            activation: 激活函数（可选，默认ReLU）
            device: 计算设备
        """
        super(Basic_MLP, self).__init__()

        # 保存网络配置参数
        self.input_shape = input_shape
        self.hidden_sizes = hidden_sizes
        self.normalize = normalize
        self.initialize = initialize
        self.activation = activation
        self.device = device

        # 设置输出形状（最后一个隐藏层的大小）
        self.output_shapes = {'state': (hidden_sizes[-1],)}

        # 构建网络结构
        self.model = self._create_network()

    def _create_network(self):
        """
        创建MLP网络结构

        Returns:
            nn.Sequential: 完整的MLP网络
        """
        layers = []
        input_shape = self.input_shape

        # 为每个隐藏层创建MLP块
        for h in self.hidden_sizes:
            # 使用mlp_block工具函数创建：线性层 + 归一化 + 激活函数
            # 等价于：
            # linear_layer = nn.Linear(input_shape[0], h)
            # activation_layer = nn.ReLU() (或其他激活函数)
            mlp, input_shape = mlp_block(
                input_shape[0],      # 输入维度
                h,                   # 输出维度
                self.normalize,      # 归一化层
                self.activation,     # 激活函数
                self.initialize,     # 初始化方法
                device=self.device   # 计算设备
            )
            layers.extend(mlp)  # 添加到层列表

        # 将所有层组合成顺序网络
        # 等价于：nn.Sequential(layer1, activation1, layer2, activation2, ...)
        return nn.Sequential(*layers)

    def forward(self, observations: np.ndarray):
        """
        前向传播：将观察转换为特征表示

        Args:
            observations (np.ndarray): 输入观察，形状如 [batch_size, input_dim]

        Returns:
            dict: 包含'state'键的字典，值为提取的特征张量

        示例：
            输入: [batch_size, 6] (停车任务的观察)
            输出: {'state': [batch_size, 256]} (256维特征)
        """
        # 将numpy数组转换为PyTorch张量
        tensor_observation = torch.as_tensor(observations, dtype=torch.float32, device=self.device)

        # 通过MLP网络进行特征提取
        features = self.model(tensor_observation)

        return {'state': features}
