#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试路径选择逻辑
验证为什么某些情况下会选择直线路径
"""

import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms/environments/single_agent_env'))

from custom_parking import SimpleHybridAStar

def test_problematic_scenarios():
    """测试可能出现问题的场景"""
    
    planner = SimpleHybridAStar()
    
    # 模拟图片中的场景：车辆在中间偏左，目标在右下角
    problematic_cases = [
        {
            "name": "图片场景模拟1：中间到右下角",
            "start": (0, 0, 0),      # 车头朝东
            "goal": (10, -5, 0),     # 目标在右下方
            "description": "车辆在中间，目标在右下角"
        },
        {
            "name": "图片场景模拟2：左侧到右侧",
            "start": (-5, 0, 0),     # 车头朝东，在左侧
            "goal": (8, -3, 0),      # 目标在右下方
            "description": "车辆在左侧，目标在右侧偏下"
        },
        {
            "name": "图片场景模拟3：类似角度小距离远",
            "start": (0, 0, 0),      # 车头朝东
            "goal": (12, -2, 0),     # 目标在右侧稍微偏下
            "description": "角度小但距离较远的情况"
        },
        {
            "name": "应该选择直线的理想情况",
            "start": (0, 0, 0),      # 车头朝东
            "goal": (4, 0.5, 0),     # 目标在正前方稍微偏移
            "description": "理想的直线停车情况"
        }
    ]
    
    print("=== 路径选择调试测试 ===\n")
    
    for i, case in enumerate(problematic_cases, 1):
        print(f"{i}. {case['name']}")
        print(f"   描述: {case['description']}")
        print(f"   起点: {case['start']}")
        print(f"   终点: {case['goal']}")
        
        # 手动计算关键参数
        x1, y1, theta1 = case['start']
        x2, y2, theta2 = case['goal']
        
        # 计算角度差
        target_direction = np.arctan2(y2 - y1, x2 - x1)
        heading_diff = target_direction - theta1
        heading_diff = np.arctan2(np.sin(heading_diff), np.cos(heading_diff))
        
        # 计算横向偏移
        lateral_offset = abs((x2 - x1) * np.sin(theta1) - (y2 - y1) * np.cos(theta1))
        
        # 计算纵向距离
        longitudinal_distance = (x2 - x1) * np.cos(theta1) + (y2 - y1) * np.sin(theta1)
        
        # 计算最终朝向差异
        final_heading_diff = abs(theta2 - theta1)
        final_heading_diff = min(final_heading_diff, 2*np.pi - final_heading_diff)
        
        # 计算总距离
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        
        print(f"   角度差: {np.degrees(abs(heading_diff)):.1f}°")
        print(f"   横向偏移: {lateral_offset:.2f}m")
        print(f"   纵向距离: {longitudinal_distance:.2f}m")
        print(f"   最终朝向差: {np.degrees(final_heading_diff):.1f}°")
        print(f"   总距离: {distance:.2f}m")
        
        # 判断路径选择逻辑
        if abs(heading_diff) < np.pi / 12:  # 角度差小于15度
            if (lateral_offset < 1.0 and 2.0 < longitudinal_distance < 6.0 and 
                final_heading_diff < np.pi/8 and distance < 8.0):
                predicted_choice = "直线路径"
                print(f"   → 预测选择: {predicted_choice} ⚠️")
            else:
                predicted_choice = "弧线路径"
                print(f"   → 预测选择: {predicted_choice} ✓")
        elif abs(heading_diff) > 2 * np.pi / 3:
            predicted_choice = "复杂路径"
            print(f"   → 预测选择: {predicted_choice} ✓")
        else:
            if distance < 8.0 and lateral_offset > 5.0:
                predicted_choice = "复杂路径"
                print(f"   → 预测选择: {predicted_choice} ✓")
            else:
                predicted_choice = "弧线路径"
                print(f"   → 预测选择: {predicted_choice} ✓")
        
        # 实际测试路径生成
        try:
            obstacles = []
            path = planner.plan(case['start'], case['goal'], obstacles)
            if path:
                print(f"   实际生成路径点数: {len(path)}")
                
                # 检查是否是直线路径（简单判断：路径点是否在一条直线上）
                if len(path) >= 3:
                    is_straight = True
                    for i in range(1, len(path)-1):
                        # 计算三点是否共线
                        p1, p2, p3 = path[i-1], path[i], path[i+1]
                        # 使用叉积判断是否共线
                        cross_product = abs((p2[0] - p1[0]) * (p3[1] - p1[1]) - (p3[0] - p1[0]) * (p2[1] - p1[1]))
                        if cross_product > 0.5:  # 容忍度
                            is_straight = False
                            break
                    
                    if is_straight:
                        print(f"   ⚠️  检测到直线路径！")
                    else:
                        print(f"   ✓  路径有适当的弯曲")
            else:
                print(f"   ✗  路径生成失败")
        except Exception as e:
            print(f"   ✗  路径生成出错: {e}")
        
        print()

if __name__ == "__main__":
    test_problematic_scenarios()
    
    print("调试总结:")
    print("1. 检查是否还有不合理的直线路径选择")
    print("2. 验证修改后的判断条件是否更严格")
    print("3. 确保只有真正适合的情况才选择直线路径")
