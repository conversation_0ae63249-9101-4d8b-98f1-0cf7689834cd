from .learner import <PERSON>rner, <PERSON>rnerMA<PERSON>
from .policy_gradient import PPOC<PERSON>IP_Learner

from .multi_agent_rl import (<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>_<PERSON>rner, MAPPO_Clip_Learner)


REGISTRY_Learners = {
    "BasicLearner": <PERSON>rner,
    "BasicLearnerMAS": <PERSON><PERSON>MA<PERSON>,
    "PPOCL<PERSON>_Learner": <PERSON><PERSON><PERSON><PERSON>_<PERSON>rner,

    "<PERSON><PERSON>_Learner": <PERSON><PERSON><PERSON><PERSON><PERSON>,
    "IPPO_Learner": <PERSON><PERSON><PERSON>Learner,
    "MAPPO_Clip_Learner": <PERSON><PERSON><PERSON>_<PERSON><PERSON>_Learner,
}

__all__ = [
    "REGISTRY_Learners", "Learner", "LearnerMAS",

     "PPOCLIP_Learner",

    "<PERSON><PERSON>_<PERSON>rner", "<PERSON><PERSON>_Learner", "<PERSON><PERSON><PERSON>_<PERSON>lip_Learner",
]
