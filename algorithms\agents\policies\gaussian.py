# ================================================================================================
# 高斯策略网络实现
#
# 这个文件实现了基于高斯分布的策略网络，主要用于连续动作空间的强化学习算法。
# 包含以下核心组件：
#
# 1. ActorPolicy - 纯Actor网络（只有策略，无价值估计）
# 2. ActorCriticPolicy - Actor-Critic网络（PPO的核心！）
# 3. PPGActorCritic - PPG算法的Actor-Critic网络
# 4. SACPolicy - SAC算法的策略网络
#
# 高斯策略的特点：
# - 输出连续动作的概率分布（均值和方差）
# - 支持自然的探索机制（通过方差控制）
# - 适用于连续控制任务（如机器人控制、自动驾驶等）
# ================================================================================================

import os                                       # 操作系统接口
import torch                                    # PyTorch 深度学习框架
import numpy as np                              # 数值计算库
from algorithms.utils import Sequence, Optional, Callable, Union  # 类型注解工具
from copy import deepcopy                       # 深拷贝函数
from gymnasium.spaces import Box                # 连续动作空间类型
from algorithms.agents import Module, Tensor, DistributedDataParallel  # 神经网络基础组件
from algorithms.agents.utils import ModuleType  # 模块类型定义
from .core import GaussianActorNet as ActorNet  # 🎭 导入高斯Actor网络
from .core import CriticNet, GaussianActorNet_SAC  # 🎯 导入Critic网络和SAC Actor网络


class ActorPolicy(Module):
    """
    Actor for stochastic policy with Gaussian distributions. (Continuous action space)

    Args:
        action_space (Box): The continuous action space.
        representation (Module): The representation module.
        actor_hidden_size (Sequence[int]): A list of hidden layer sizes for actor network.
        normalize (Optional[ModuleType]): The layer normalization over a minibatch of inputs.
        initialize (Optional[Callable[..., Tensor]]): The parameters initializer.
        activation (Optional[ModuleType]): The activation function for each layer.
        activation_action (Optional[ModuleType]): The activation of final layer to bound the actions.
        device (Optional[Union[str, int, torch.device]]): The calculating device.
        use_distributed_training (bool): Whether to use multi-GPU for distributed training.
    """

    def __init__(self,
                 action_space: Box,
                 representation: Module,
                 actor_hidden_size: Sequence[int] = None,
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 activation_action: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None,
                 use_distributed_training: bool = False,
                 fixed_std: bool = True):
        super(ActorPolicy, self).__init__()
        self.action_dim = action_space.shape[0]
        self.representation = representation
        self.representation_info_shape = self.representation.output_shapes
        self.actor = ActorNet(representation.output_shapes['state'][0], self.action_dim, actor_hidden_size,
                              normalize, initialize, activation, activation_action, device)

        # Prepare DDP module.
        self.distributed_training = use_distributed_training
        if self.distributed_training:
            self.rank = int(os.environ["RANK"])
            if self.representation._get_name() != "Basic_Identical":
                self.representation = DistributedDataParallel(module=self.representation, device_ids=[self.rank])
            self.actor = DistributedDataParallel(module=self.actor, device_ids=[self.rank])

    def forward(self, observation: Union[np.ndarray, dict]):
        """
        Returns the hidden states, action distribution.

        Parameters:
            observation: The original observation of agent.

        Returns:
            outputs: The outputs of representation.
            a_dist: The distribution of actions output by actor.
        """
        outputs = self.representation(observation)
        a_dist = self.actor(outputs['state'])
        return outputs, a_dist, None


class ActorCriticPolicy(Module):
    """
    PPO算法的核心：高斯Actor-Critic策略网络

    这是PPO算法中使用的主要策略网络，结合了Actor和Critic两个组件：

    Actor部分：
    - 输入状态，输出高斯分布N(μ, σ²)
    - 用于策略改进，学习最优动作选择
    - 支持连续动作空间（如转向角度、加速度等）

    Critic部分：
    - 输入状态，输出状态价值V(s)
    - 用于策略评估，估计当前状态的好坏
    - 为Actor提供基线，减少方差

    网络结构：
    观察 → 表示网络 → 特征 → Actor → 高斯分布
                           ↘ Critic → 状态价值

    Args:
        action_space (Box): 连续动作空间，定义动作的维度和范围
        representation (Module): 表示网络，用于特征提取（如MLP、CNN等）
        actor_hidden_size (Sequence[int]): Actor网络的隐藏层大小列表
        critic_hidden_size (Sequence[int]): Critic网络的隐藏层大小列表
        normalize (Optional[ModuleType]): 层归一化函数
        initialize (Optional[Callable]): 参数初始化函数
        activation (Optional[ModuleType]): 激活函数
        activation_action (Optional[ModuleType]): 动作输出层的激活函数
        device (Optional[Union[str, int, torch.device]]): 计算设备
        use_distributed_training (bool): 是否使用多GPU分布式训练
    """

    def __init__(self,
                 action_space: Box,
                 representation: Module,
                 actor_hidden_size: Sequence[int] = None,
                 critic_hidden_size: Sequence[int] = None,
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 activation_action: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None,
                 use_distributed_training: bool = False):
        """
        初始化Actor-Critic策略网络

        构建PPO算法所需的完整网络架构，包括特征提取、策略输出和价值估计
        """
        super(ActorCriticPolicy, self).__init__()

        # ========================================================================================
        # 基础配置
        # ========================================================================================

        # 获取动作空间维度（如2D：[转向角度, 加速度]）
        self.action_dim = action_space.shape[0]

        # 保存表示网络（特征提取器）
        self.representation = representation
        self.representation_info_shape = representation.output_shapes

        # ========================================================================================
        # 构建Actor网络
        # ========================================================================================

        # 创建高斯Actor网络：状态 → 高斯分布N(μ, σ²)
        self.actor = ActorNet(
            representation.output_shapes['state'][0],  # 输入特征维度
            self.action_dim,                          # 输出动作维度
            actor_hidden_size,                        # 隐藏层配置
            normalize, initialize, activation,        # 网络配置
            activation_action,                        # 动作激活函数
            device                                    # 计算设备
        )

        # ========================================================================================
        # 构建Critic网络
        # ========================================================================================

        # 创建Critic网络：状态 → 状态价值V(s)
        self.critic = CriticNet(
            representation.output_shapes['state'][0],  # 输入特征维度
            critic_hidden_size,                       # 隐藏层配置
            normalize, initialize, activation,        # 网络配置
            device                                    # 计算设备
        )

        # ========================================================================================
        # 分布式训练配置
        # ========================================================================================

        # 配置分布式数据并行（DDP）
        self.distributed_training = use_distributed_training
        if self.distributed_training:
            # 获取当前进程的rank
            self.rank = int(os.environ["RANK"])

            # 为各个网络组件配置DDP
            if self.representation._get_name() != "Basic_Identical":
                self.representation = DistributedDataParallel(module=self.representation, device_ids=[self.rank])
            self.actor = DistributedDataParallel(module=self.actor, device_ids=[self.rank])
            self.critic = DistributedDataParallel(module=self.critic, device_ids=[self.rank])

    def forward(self, observation: Union[np.ndarray, dict]):
        """
        Actor-Critic网络前向传播 - PPO的核心计算

        这是PPO算法中最关键的函数，同时计算策略分布和状态价值：

        计算流程：
        1. 特征提取：观察 → 表示网络 → 特征向量
        2. 策略计算：特征 → Actor → 高斯分布N(μ, σ²)
        3. 价值估计：特征 → Critic → 状态价值V(s)

        Args:
            observation (Union[np.ndarray, dict]): 智能体的原始观察
                - 对于停车任务：可能包含车辆位置、速度、目标位置等
                - 形状通常为 [batch_size, obs_dim]

        Returns:
            tuple: 包含三个元素的元组
                - outputs (dict): 表示网络的输出特征，包含中间表示
                - a_dist (DiagGaussianDistribution): Actor输出的动作分布
                    * 可用于采样动作：action = a_dist.sample()
                    * 可用于计算概率：log_prob = a_dist.log_prob(action)
                - value (Tensor): Critic输出的状态价值V(s)，形状为 [batch_size]
        """
        # ========================================================================================
        # 步骤1: 特征提取
        # ========================================================================================

        # 通过表示网络提取特征（如MLP、CNN等）
        outputs = self.representation(observation)

        # ========================================================================================
        # 步骤2: 策略计算
        # ========================================================================================

        # Actor网络：特征 → 高斯分布N(μ, σ²)
        a_dist = self.actor(outputs['state'])

        # ========================================================================================
        # 步骤3: 价值估计
        # ========================================================================================

        # Critic网络：特征 → 状态价值V(s)
        v = self.critic(outputs['state'])

        # 返回特征、动作分布和状态价值
        # v[:, 0] 是因为Critic输出形状为[batch_size, 1]，需要压缩为[batch_size]
        return outputs, a_dist, v[:, 0]


class PPGActorCritic(Module):
    """
    Actor-Critic for PPG with Gaussian distributions. (Continuous action space)

    Args:
        action_space (Box): The continuous action space.
        representation (Module): The representation module.
        actor_hidden_size (Sequence[int]): A list of hidden layer sizes for actor network.
        critic_hidden_size (Sequence[int]): A list of hidden layer sizes for critic network.
        normalize (Optional[ModuleType]): The layer normalization over a minibatch of inputs.
        initialize (Optional[Callable[..., Tensor]]): The parameters initializer.
        activation (Optional[ModuleType]): The activation function for each layer.
        activation_action (Optional[ModuleType]): The activation of final layer to bound the actions.
        device (Optional[Union[str, int, torch.device]]): The calculating device.
        use_distributed_training (bool): Whether to use multi-GPU for distributed training.
    """

    def __init__(self,
                 action_space: Box,
                 representation: Module,
                 actor_hidden_size: Sequence[int] = None,
                 critic_hidden_size: Sequence[int] = None,
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 activation_action: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None,
                 use_distributed_training: bool = False):
        super(PPGActorCritic, self).__init__()
        self.action_dim = action_space.shape[0]
        self.actor_representation = representation
        self.critic_representation = deepcopy(representation)
        self.representation_info_shape = self.actor_representation.output_shapes
        self.actor = ActorNet(representation.output_shapes['state'][0], self.action_dim, actor_hidden_size,
                              normalize, initialize, activation, activation_action, device)
        self.critic = CriticNet(representation.output_shapes['state'][0], critic_hidden_size,
                                normalize, initialize, activation, device)
        self.aux_critic = CriticNet(representation.output_shapes['state'][0], critic_hidden_size,
                                    normalize, initialize, activation, device)

        # Prepare DDP module.
        self.distributed_training = use_distributed_training
        if self.distributed_training:
            self.rank = int(os.environ["RANK"])
            if self.representation._get_name() != "Basic_Identical":
                self.representation = DistributedDataParallel(module=self.representation, device_ids=[self.rank])
            self.actor = DistributedDataParallel(module=self.actor, device_ids=[self.rank])
            self.critic = DistributedDataParallel(module=self.critic, device_ids=[self.rank])
            self.aux_critic = DistributedDataParallel(module=self.aux_critic, device_ids=[self.rank])

    def forward(self, observation: Union[np.ndarray, dict]):
        """
        Returns the actors representation output, action distribution, values, and auxiliary values.

        Parameters:
            observation: The original observation of agent.

        Returns:
            policy_outputs: The outputs of actor representation.
            a_dist: The distribution of actions output by actor.
            value: The state values output by critic.
            aux_value: The auxiliary values output by aux_critic.
        """
        policy_outputs = self.actor_representation(observation)
        critic_outputs = self.critic_representation(observation)
        a_dist = self.actor(policy_outputs['state'])
        value = self.critic(critic_outputs['state'])
        aux_value = self.aux_critic(policy_outputs['state'])
        return policy_outputs, a_dist, value[:, 0], aux_value[:, 0]


class SACPolicy(Module):
    """
    Actor-Critic for SAC with Gaussian distributions. (Continuous action space)

    Args:
        action_space (Box): The continuous action space.
        representation (Module): The representation module.
        actor_hidden_size (Sequence[int]): A list of hidden layer sizes for actor network.
        critic_hidden_size (Sequence[int]): A list of hidden layer sizes for critic network.
        normalize (Optional[ModuleType]): The layer normalization over a minibatch of inputs.
        initialize (Optional[Callable[..., Tensor]]): The parameters initializer.
        activation (Optional[ModuleType]): The activation function for each layer.
        activation_action (Optional[ModuleType]): The activation of final layer to bound the actions.
        device (Optional[Union[str, int, torch.device]]): The calculating device.
        use_distributed_training (bool): Whether to use multi-GPU for distributed training.
    """

    def __init__(self,
                 action_space: Box,
                 representation: Module,
                 actor_hidden_size: Sequence[int],
                 critic_hidden_size: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 activation_action: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None,
                 use_distributed_training: bool = False):
        super(SACPolicy, self).__init__()
        self.action_space = action_space
        self.action_dim = action_space.shape[0]
        self.representation_info_shape = representation.output_shapes

        self.actor_representation = representation
        self.actor = GaussianActorNet_SAC(representation.output_shapes['state'][0], self.action_dim, actor_hidden_size,
                                          normalize, initialize, activation, activation_action, device)

        self.critic_1_representation = deepcopy(representation)
        self.critic_1 = CriticNet(representation.output_shapes['state'][0] + self.action_dim, critic_hidden_size,
                                  normalize, initialize, activation, device)
        self.critic_2_representation = deepcopy(representation)
        self.critic_2 = CriticNet(representation.output_shapes['state'][0] + self.action_dim, critic_hidden_size,
                                  normalize, initialize, activation, device)
        self.target_critic_1_representation = deepcopy(self.critic_1_representation)
        self.target_critic_1 = deepcopy(self.critic_1)
        self.target_critic_2_representation = deepcopy(self.critic_2_representation)
        self.target_critic_2 = deepcopy(self.critic_2)

        self.actor_parameters = list(self.actor_representation.parameters()) + list(self.actor.parameters())
        self.critic_parameters = list(self.critic_1_representation.parameters()) + list(
            self.critic_1.parameters()) + list(self.critic_2_representation.parameters()) + list(
            self.critic_2.parameters())

        # Prepare DDP module.
        self.distributed_training = use_distributed_training
        if self.distributed_training:
            self.rank = int(os.environ["RANK"])
            if self.actor_representation._get_name() != "Basic_Identical":
                self.actor_representation = DistributedDataParallel(self.actor_representation, device_ids=[self.rank])
            if self.critic_1_representation._get_name() != "Basic_Identical":
                self.critic_1_representation = DistributedDataParallel(self.critic_1_representation,
                                                                       device_ids=[self.rank])
            if self.critic_2_representation._get_name() != "Basic_Identical":
                self.critic_2_representation = DistributedDataParallel(self.critic_2_representation,
                                                                       device_ids=[self.rank])
            self.actor = DistributedDataParallel(module=self.actor, device_ids=[self.rank])
            self.critic_1 = DistributedDataParallel(module=self.critic_1, device_ids=[self.rank])
            self.critic_2 = DistributedDataParallel(module=self.critic_2, device_ids=[self.rank])

    def forward(self, observation: Union[np.ndarray, dict]):
        """
        Returns the output of actor representation and samples of actions.

        Parameters:
            observation: The original observation of an agent.

        Returns:
            outputs: The outputs of the actor representation.
            act_sample: The sampled actions from the distribution output by the actor.
        """
        outputs = self.actor_representation(observation)
        act_dist = self.actor(outputs['state'])
        act_sample = act_dist.activated_rsample()
        return outputs, act_sample

    def Qpolicy(self, observation: Union[np.ndarray, dict]):
        """
        Feedforward and calculate the log of action probabilities, and Q-values.

        Parameters:
            observation: The original observation of an agent.

        Returns:
            log_action_prob: The log of action probabilities.
            q_1: The Q-value calculated by the first critic network.
            q_2: The Q-value calculated by the other critic network.
        """
        outputs_actor = self.actor_representation(observation)
        outputs_critic_1 = self.critic_1_representation(observation)
        outputs_critic_2 = self.critic_2_representation(observation)

        act_dist = self.actor(outputs_actor['state'])
        act_sample, log_action_prob = act_dist.activated_rsample_and_logprob()

        q_1 = self.critic_1(torch.concat([outputs_critic_1['state'], act_sample], dim=-1))
        q_2 = self.critic_2(torch.concat([outputs_critic_2['state'], act_sample], dim=-1))
        return log_action_prob, q_1[:, 0], q_2[:, 0]

    def Qtarget(self, observation: Union[np.ndarray, dict]):
        """
        Calculate the log of action probabilities and Q-values with target networks.

        Parameters:
            observation: The original observation of an agent.

        Returns:
            log_action_prob: The log of action probabilities.
            target_q: The minimum of Q-values calculated by the target critic networks.
        """
        outputs_actor = self.actor_representation(observation)
        outputs_critic_1 = self.target_critic_1_representation(observation)
        outputs_critic_2 = self.target_critic_2_representation(observation)

        new_act_dist = self.actor(outputs_actor['state'])
        new_act_sample, log_action_prob = new_act_dist.activated_rsample_and_logprob()

        target_q_1 = self.target_critic_1(torch.concat([outputs_critic_1['state'], new_act_sample], dim=-1))
        target_q_2 = self.target_critic_2(torch.concat([outputs_critic_2['state'], new_act_sample], dim=-1))
        target_q = torch.min(target_q_1, target_q_2)
        return log_action_prob, target_q[:, 0]

    def Qaction(self, observation: Union[np.ndarray, dict], action: Tensor):
        """
        Returns the evaluated Q-values for current observation-action pairs.

        Parameters:
            observation: The original observation.
            action: The selected actions.

        Returns:
            q_1: The Q-value calculated by the first critic network.
            q_2: The Q-value calculated by the other critic network.
        """
        outputs_critic_1 = self.critic_1_representation(observation)
        outputs_critic_2 = self.critic_2_representation(observation)
        q_1 = self.critic_1(torch.concat([outputs_critic_1['state'], action], dim=-1))
        q_2 = self.critic_2(torch.concat([outputs_critic_2['state'], action], dim=-1))
        return q_1[:, 0], q_2[:, 0]

    def soft_update(self, tau=0.005):
        for ep, tp in zip(self.critic_1_representation.parameters(), self.target_critic_1_representation.parameters()):
            tp.data.mul_(1 - tau)
            tp.data.add_(tau * ep.data)
        for ep, tp in zip(self.critic_2_representation.parameters(), self.target_critic_2_representation.parameters()):
            tp.data.mul_(1 - tau)
            tp.data.add_(tau * ep.data)
        for ep, tp in zip(self.critic_1.parameters(), self.target_critic_1.parameters()):
            tp.data.mul_(1 - tau)
            tp.data.add_(tau * ep.data)
        for ep, tp in zip(self.critic_2.parameters(), self.target_critic_2.parameters()):
            tp.data.mul_(1 - tau)
            tp.data.add_(tau * ep.data)
