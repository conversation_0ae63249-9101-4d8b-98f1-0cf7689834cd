from .core import Basic<PERSON><PERSON>
from .core import ActorNet
from .core import CategoricalActorNet
from .core import CategoricalActorNet_SAC
from .core import G<PERSON><PERSON><PERSON>ctor<PERSON>
from .core import CriticNet
from .core import GaussianActorNet_SAC
from .core import VDN_mixer
from .core import QMIX_mixer
from .core import QMIX_FF_mixer
from .core import QTRAN_base
from .core import QTRAN_alt

from .categorical import ActorCriticPolicy as Categorical_AC_Policy
from .categorical import ActorPolicy as Categorical_Actor_Policy
from .categorical import PPGActorCritic as Categorical_PPG_Policy
from .categorical import SACDISPolicy as Categorical_SAC_Policy

from .gaussian import ActorCriticPolicy as Gaussian_AC_Policy
from .gaussian import ActorPolicy as Gaussian_Actor_Policy
from .gaussian import PPGActorCritic as Gaussian_PPG_Policy
from .gaussian import SACPolicy as Gaussian_SAC_Policy


from .categorical_marl import MeanFieldActorCriticPolicy as Categorical_MFAC_Policy
from .categorical_marl import COMA_Policy, IC3Net_Policy, CommNet_Policy, TarMAC_Policy
from .categorical_marl import MAAC_Policy as Categorical_MAAC_Policy
from .categorical_marl import MAAC_Policy_Share as Categorical_MAAC_Policy_Share
from .categorical_marl import Basic_ISAC_Policy as Categorical_ISAC
from .categorical_marl import MASAC_Policy as Categorical_MASAC
from .gaussian_marl import Basic_ISAC_Policy as Gaussian_ISAC
from .gaussian_marl import MASAC_Policy as Gaussian_MASAC
from .gaussian_marl import MAAC_Policy as Gaussain_MAAC

Mixer = {
    "VDN": VDN_mixer,
    "QMIX": QMIX_mixer,
    "WQMIX": QMIX_FF_mixer,
    "QTRAN_alt": QTRAN_alt,
    "QTRAN_base": QTRAN_base
}

REGISTRY_Policy = {
    # ↓ Single-Agent DRL ↓ #
    "Categorical_AC": Categorical_AC_Policy,
    "Categorical_Actor": Categorical_Actor_Policy,
    "Categorical_PPG": Categorical_PPG_Policy,
    "Categorical_SAC": Categorical_SAC_Policy,
    "Gaussian_AC": Gaussian_AC_Policy,
    "Gaussian_SAC": Gaussian_SAC_Policy,
    "Gaussian_PPG": Gaussian_PPG_Policy,
    "Gaussian_Actor": Gaussian_Actor_Policy,

    # ↓ Multi-Agent DRL ↓ #
    "Categorical_MAAC_Policy": Categorical_MAAC_Policy,
    "Categorical_MAAC_Policy_Share": Categorical_MAAC_Policy_Share,
    "Categorical_COMA_Policy": COMA_Policy,
    "Categorical_ISAC_Policy": Categorical_ISAC,
    "Categorical_MASAC_Policy": Categorical_MASAC,
    "Categorical_MFAC_Policy": Categorical_MFAC_Policy,
    "Gaussian_MAAC_Policy": Gaussain_MAAC,
    "Gaussian_ISAC_Policy": Gaussian_ISAC,
    "Gaussian_MASAC_Policy": Gaussian_MASAC,
    "IC3Net_Policy": IC3Net_Policy,
    "CommNet_Policy": CommNet_Policy,
    "TarMAC_Policy": TarMAC_Policy,
}

__all__ = [
    "REGISTRY_Policy", "Mixer",
    "ActorNet", "CategoricalActorNet", "CategoricalActorNet_SAC", "GaussianActorNet", "GaussianActorNet_SAC",
    "BasicQhead", "CriticNet", "GaussianActorNet_SAC",
    "VDN_mixer", "QMIX_mixer", "QMIX_FF_mixer", "QTRAN_base", "QTRAN_alt",
    "Categorical_AC_Policy", "Categorical_Actor_Policy", "Categorical_PPG_Policy", "Categorical_SAC_Policy",
    "Gaussian_AC_Policy", "Gaussian_Actor_Policy", "Gaussian_PPG_Policy", "Gaussian_SAC_Policy",
    "COMA_Policy", "Categorical_MAAC_Policy", "Categorical_MAAC_Policy_Share",
    "IC3Net_Policy", "CommNet_Policy", "TarMAC_Policy",
    "Categorical_ISAC", "Categorical_MASAC",
    "Gaussian_ISAC", "Gaussian_MASAC", "Gaussain_MAAC",
]
