# ================================================================================================
# 强化学习智能体基础类
#
# 这个文件实现了所有强化学习智能体的基础框架，提供了：
# 1. 智能体的基本生命周期管理（初始化、训练、测试、保存/加载）
# 2. 环境交互的标准接口
# 3. 网络模型的构建和管理
# 4. 日志记录和可视化支持
# 5. 分布式训练支持
# 6. 观察和奖励的归一化处理
#
# 这是整个强化学习框架的核心基类，所有具体算法都继承自这个类。
# ================================================================================================

import os                                            # 操作系统接口
import torch                                         # PyTorch 深度学习框架
import wandb                                         # Weights & Biases 实验跟踪
import socket                                        # 网络通信
import numpy as np                                   # 数值计算库
import torch.distributed as dist                     # PyTorch 分布式训练
from abc import ABC                                  # 抽象基类
from pathlib import Path                             # 路径处理
from argparse import Namespace                       # 命名空间类
from mpi4py import MPI                              # MPI 并行计算接口
from gymnasium.spaces import Dict, Space            # Gymnasium 环境空间定义
from torch.utils.tensorboard import SummaryWriter   # TensorBoard 日志记录
from torch.distributed import destroy_process_group # 分布式进程组管理
from algorithms.utils import get_time_string, create_directory, RunningMeanStd, space2shape, EPS, Optional, Union  # 工具函数
from algorithms.environments import DummyVecEnv, SubprocVecEnv  # 向量化环境
from algorithms.agents import REGISTRY_Representation, REGISTRY_Learners, Module  # 网络组件注册表
from algorithms.agents.utils import nn, NormalizeFunctions, ActivationFunctions, init_distributed_mode  # 神经网络工具
from .callback import BaseCallback                   # 回调函数基类


class Agent(ABC):
    """
    强化学习智能体的基础抽象类

    这是所有强化学习算法的基础类，定义了智能体的标准接口和通用功能：

    🎯 **核心功能**：
    1. **环境交互** - 与环境进行标准化的交互接口
    2. **网络管理** - 策略网络和价值网络的构建与管理
    3. **训练流程** - 标准化的训练和测试流程
    4. **数据处理** - 观察和奖励的归一化处理
    5. **日志记录** - TensorBoard和WandB的集成支持
    6. **模型保存** - 模型的保存和加载功能
    7. **分布式训练** - 多GPU/多节点训练支持

    🏗️ **设计模式**：
    - 使用抽象基类(ABC)确保子类实现必要方法
    - 模板方法模式：定义算法骨架，子类实现具体细节
    - 策略模式：支持不同的网络结构和学习算法

    Args:
        config (Namespace): 配置参数，包含所有超参数和设置
        envs (Union[DummyVecEnv, SubprocVecEnv]): 向量化环境，支持并行环境
        callback (Optional[BaseCallback]): 用户自定义回调函数，用于：
            - 训练过程监控和日志记录
            - 早停和模型检查点保存
            - 自定义训练逻辑注入
            - 实验结果可视化
    """

    def __init__(self,
                 config: Namespace,
                 envs: Union[DummyVecEnv, SubprocVecEnv],
                 callback: Optional[BaseCallback] = None):
        """
        初始化强化学习智能体

        设置智能体的所有核心组件和配置参数
        """

        # ========================================================================================
        # 训练设置和配置参数
        # ========================================================================================

        # 保存配置对象
        self.config = config

        # 网络架构设置
        self.use_rnn = getattr(config, "use_rnn", False)                    # 是否使用RNN网络
        self.use_actions_mask = getattr(config, "use_actions_mask", False)  # 是否使用动作掩码

        # 分布式训练设置
        self.distributed_training = getattr(config, "distributed_training", False)
        if self.distributed_training:
            # 分布式训练模式：从环境变量获取分布式信息
            self.world_size = int(os.environ['WORLD_SIZE'])  # 总进程数
            self.rank = int(os.environ['RANK'])              # 当前进程排名
            master_port = getattr(config, "master_port", None)
            init_distributed_mode(master_port=master_port)   # 初始化分布式模式
        else:
            # 单机训练模式
            self.world_size = 1
            self.rank = 0

        # 核心训练参数
        self.gamma = config.gamma                                          # 折扣因子γ
        self.start_training = getattr(config, "start_training", 1)         # 开始训练的步数
        self.training_frequency = getattr(config, "training_frequency", 1) # 训练频率
        self.n_epochs = getattr(config, "n_epochs", 1)                    # 每次更新的轮数
        self.device = config.device                                        # 计算设备(CPU/GPU)

        # ========================================================================================
        # 环境属性和接口
        # ========================================================================================

        # 环境管理
        self.envs = envs                                                   # 向量化环境
        self.envs.reset()                                                  # 重置环境到初始状态
        self.episode_length = self.config.episode_length = envs.max_episode_steps  # 最大回合长度

        # 渲染和可视化设置
        self.render = config.render                                        # 是否渲染环境
        self.fps = config.fps                                             # 渲染帧率

        # 环境空间信息
        self.n_envs = envs.num_envs                                       # 并行环境数量
        self.observation_space = envs.observation_space                   # 观察空间
        self.action_space = envs.action_space                             # 动作空间

        # 训练状态跟踪
        self.current_step = 0                                             # 当前训练步数
        self.current_episode = np.zeros((self.n_envs,), np.int32)        # 每个环境的回合数

        # ========================================================================================
        # 观察和奖励归一化设置
        # ========================================================================================

        # MPI通信设置（用于分布式归一化统计）
        self.comm = MPI.COMM_WORLD

        # 观察归一化：使用运行时均值和标准差归一化观察
        self.obs_rms = RunningMeanStd(shape=space2shape(self.observation_space),
                                     comm=self.comm, use_mpi=False)

        # 奖励归一化：使用运行时均值和标准差归一化奖励
        self.ret_rms = RunningMeanStd(shape=(), comm=self.comm, use_mpi=False)

        # 归一化配置参数
        self.use_obsnorm = config.use_obsnorm                             # 是否使用观察归一化
        self.use_rewnorm = config.use_rewnorm                             # 是否使用奖励归一化
        self.obsnorm_range = config.obsnorm_range                         # 观察归一化范围
        self.rewnorm_range = config.rewnorm_range                         # 奖励归一化范围

        # 累积奖励跟踪（用于奖励归一化）
        self.returns = np.zeros((self.envs.num_envs,), np.float32)

        # ========================================================================================
        # 目录准备和时间戳同步
        # ========================================================================================

        # 生成时间戳（分布式训练时需要同步）
        if self.distributed_training and self.world_size > 1:
            if self.rank == 0:
                # 主进程生成时间戳
                time_string = get_time_string()
                time_string_tensor = torch.tensor(list(time_string.encode('utf-8')),
                                                dtype=torch.uint8).to(self.rank)
            else:
                # 从进程等待接收时间戳
                time_string_tensor = torch.zeros(16, dtype=torch.uint8).to(self.rank)

            # 广播时间戳到所有进程，确保一致性
            dist.broadcast(time_string_tensor, src=0)
            time_string = bytes(time_string_tensor.cpu().tolist()).decode('utf-8').rstrip('\x00')
        else:
            # 单机训练直接生成时间戳
            time_string = get_time_string()

        # 构建模型保存路径
        seed = f"seed_{self.config.seed}_"
        self.model_dir_load = config.model_dir                            # 模型加载路径
        self.model_dir_save = os.path.join(os.getcwd(), config.model_dir, # 模型保存路径
                                          seed + time_string)

        # ========================================================================================
        # 日志记录器创建和配置
        # ========================================================================================

        # 根据配置选择日志记录方式
        if config.logger == "tensorboard":
            # TensorBoard 日志记录
            log_dir = os.path.join(os.getcwd(), config.log_dir, seed + time_string)

            # 分布式训练时的目录创建同步
            if self.rank == 0:
                create_directory(log_dir)  # 主进程创建目录
            else:
                while not os.path.exists(log_dir):
                    pass  # 从进程等待主进程创建完成

            self.writer = SummaryWriter(log_dir)  # 创建TensorBoard写入器
            self.use_wandb = False

        elif config.logger == "wandb":
            # Weights & Biases 日志记录
            config_dict = vars(config)  # 将配置转换为字典
            log_dir = config.log_dir
            wandb_dir = Path(os.path.join(os.getcwd(), config.log_dir))

            # 分布式训练时的目录创建同步
            if self.rank == 0:
                create_directory(str(wandb_dir))  # 主进程创建目录
            else:
                while not os.path.exists(str(wandb_dir)):
                    pass  # 从进程等待主进程创建完成

            # 初始化WandB实验跟踪
            wandb.init(config=config_dict,                    # 实验配置
                       project=config.project_name,          # 项目名称
                       entity=config.wandb_user_name,        # 用户/团队名称
                       notes=socket.gethostname(),           # 运行主机信息
                       dir=wandb_dir,                        # 日志目录
                       group=config.env_id,                  # 实验分组
                       job_type=config.agent,                # 任务类型
                       name=time_string,                     # 运行名称
                       reinit=True,                          # 允许重新初始化
                       settings=wandb.Settings(start_method="fork")  # 进程启动方式
                       )
            # os.environ["WANDB_SILENT"] = "True"  # 可选：静默模式
            self.use_wandb = True

        else:
            raise AttributeError(f"不支持的日志记录器: {config.logger}。支持的选项: 'tensorboard', 'wandb'")
        self.log_dir = log_dir

        # ========================================================================================
        # 核心组件准备
        # ========================================================================================

        # 智能体的核心组件（将在子类中初始化）
        self.policy: Optional[Module] = None      # 策略网络（演员网络）
        self.learner: Optional[Module] = None     # 学习器（包含优化器和损失函数）
        self.memory: Optional[object] = None      # 经验缓冲区

        # 回调函数：用于自定义训练逻辑和监控
        self.callback = callback or BaseCallback()

    def save_model(self, model_name):
        """
        保存训练好的模型

        保存神经网络参数和观察归一化统计信息，确保模型可以完整恢复。
        在分布式训练中，只有主进程(rank=0)负责保存模型。

        Args:
            model_name (str): 模型文件名
        """
        # 分布式训练时只有主进程保存模型
        if self.distributed_training:
            if self.rank > 0:
                return

        # 创建模型保存目录
        if not os.path.exists(self.model_dir_save):
            os.makedirs(self.model_dir_save)

        # 保存神经网络参数
        model_path = os.path.join(self.model_dir_save, model_name)
        self.learner.save_model(model_path)

        # 保存观察归一化统计信息
        if self.use_obsnorm:
            obs_norm_path = os.path.join(self.model_dir_save, "obs_rms.npy")
            observation_stat = {
                'count': self.obs_rms.count,  # 观察样本数量
                'mean': self.obs_rms.mean,    # 观察均值
                'var': self.obs_rms.var       # 观察方差
            }
            np.save(obs_norm_path, observation_stat)

    def load_model(self, path, model=None):
        """
        加载预训练模型

        加载神经网络参数和观察归一化统计信息，确保模型状态完整恢复。

        Args:
            path (str): 模型文件路径
            model: 可选的模型对象
        """
        # 加载神经网络参数
        path_loaded = self.learner.load_model(path, model)

        # 恢复观察归一化统计信息
        if self.use_obsnorm:
            obs_norm_path = os.path.join(path_loaded, "obs_rms.npy")
            if os.path.exists(obs_norm_path):
                try:
                    # 使用绝对路径来避免Windows路径问题
                    abs_obs_norm_path = os.path.abspath(obs_norm_path)
                    observation_stat = np.load(abs_obs_norm_path, allow_pickle=True).item()
                    self.obs_rms.count = observation_stat['count']
                    self.obs_rms.mean = observation_stat['mean']
                    self.obs_rms.var = observation_stat['var']
                except Exception as e:
                    print(f"警告: 加载观察归一化文件失败: {e}")
                    print(f"将使用默认的观察归一化参数")
            else:
                raise RuntimeError(f"无法从 {obs_norm_path} 加载观察归一化文件 'obs_rms.npy'！")

    def log_infos(self, info: dict, x_index: int):
        """
        记录训练信息到日志系统

        将训练过程中的各种指标（损失、奖励、准确率等）记录到
        TensorBoard或WandB中，用于监控和分析训练过程。

        Args:
            info (dict): 要记录的信息字典，键为指标名称，值为指标值
            x_index (int): 当前步数，用作横坐标
        """
        if self.use_wandb:
            # 使用WandB记录
            for k, v in info.items():
                if v is None:
                    continue
                wandb.log({k: v}, step=x_index)
        else:
            # 使用TensorBoard记录
            for k, v in info.items():
                if v is None:
                    continue
                try:
                    # 尝试记录标量值
                    self.writer.add_scalar(k, v, x_index)
                except:
                    # 如果是多个标量，使用add_scalars
                    self.writer.add_scalars(k, v, x_index)

    def log_videos(self, info: dict, fps: int, x_index: int = 0):
        """
        记录视频到日志系统

        将训练过程中的视频（如智能体行为演示）记录到日志系统中。

        Args:
            info (dict): 视频信息字典，键为视频名称，值为视频数据
            fps (int): 视频帧率
            x_index (int): 当前步数
        """
        if self.use_wandb:
            # 使用WandB记录视频
            for k, v in info.items():
                if v is None:
                    continue
                wandb.log({k: wandb.Video(v, fps=fps, format='gif')}, step=x_index)
        else:
            # 使用TensorBoard记录视频
            for k, v in info.items():
                if v is None:
                    continue
                self.writer.add_video(k, v, fps=fps, global_step=x_index)

    def _process_observation(self, observations):
        """
        处理和归一化观察数据

        使用运行时统计信息对观察进行归一化处理，提高训练稳定性。
        支持字典类型和数组类型的观察空间。

        Args:
            observations: 原始观察数据

        Returns:
            处理后的观察数据
        """
        if self.use_obsnorm:
            if isinstance(self.observation_space, Dict):
                # 字典类型观察空间：分别归一化每个键
                for key in self.observation_space.spaces.keys():
                    observations[key] = np.clip(
                        (observations[key] - self.obs_rms.mean[key]) / (self.obs_rms.std[key] + EPS),
                        -self.obsnorm_range, self.obsnorm_range)
            else:
                # 数组类型观察空间：直接归一化
                observations = np.clip((observations - self.obs_rms.mean) / (self.obs_rms.std + EPS),
                                       -self.obsnorm_range, self.obsnorm_range)
            return observations
        else:
            # 不使用归一化，直接返回原始观察
            return observations

    def _process_reward(self, rewards):
        """
        处理和归一化奖励数据

        使用运行时统计信息对奖励进行归一化处理，防止奖励尺度过大或过小
        影响训练效果。

        Args:
            rewards: 原始奖励数据

        Returns:
            处理后的奖励数据
        """
        if self.use_rewnorm:
            # 使用标准差归一化奖励，并限制在合理范围内
            std = np.clip(self.ret_rms.std, 0.1, 100)  # 防止标准差过小或过大
            return np.clip(rewards / std, -self.rewnorm_range, self.rewnorm_range)
        else:
            # 不使用归一化，直接返回原始奖励
            return rewards

    def _build_representation(self, representation_key: str,
                              input_space: Optional[Space],
                              config: Namespace) -> Module:
        """
        构建策略网络的表示层

        根据配置创建神经网络的表示层（特征提取层），支持多种网络架构：
        - MLP: 多层感知机
        - CNN: 卷积神经网络
        - RNN: 循环神经网络
        - Transformer: 注意力机制网络

        Args:
            representation_key (str): 表示层类型，如 "Basic_MLP", "Basic_RNN" 等
            input_space (Optional[Space]): 输入张量的空间定义
            config (Namespace): 创建表示层模块的配置参数

        Returns:
            Module: 构建好的表示层模块
        """
        input_representations = dict(
            input_shape=space2shape(input_space),
            hidden_sizes=getattr(config, "representation_hidden_size", None),
            normalize=NormalizeFunctions[config.normalize] if hasattr(config, "normalize") else None,
            initialize=nn.init.orthogonal_,
            activation=ActivationFunctions[config.activation],
            kernels=getattr(config, "kernels", None),
            strides=getattr(config, "strides", None),
            filters=getattr(config, "filters", None),
            fc_hidden_sizes=getattr(config, "fc_hidden_sizes", None),
            image_patch_size=getattr(config, "image_patch_size", None),
            frame_patch_size=getattr(config, "frame_patch_size", None),
            final_dim=getattr(config, "final_dim", None),
            embedding_dim=getattr(config, "embedding_dim", None),
            depth=getattr(config, "depth", None),
            heads=getattr(config, "heads", None),
            FFN_dim=getattr(config, "FFN_dim", None),
            device=self.device)
        representation = REGISTRY_Representation[representation_key](**input_representations)
        if representation_key not in REGISTRY_Representation:
            raise AttributeError(f"{representation_key} is not registered in REGISTRY_Representation.")
        return representation

    def _build_policy(self) -> Module:
        """
        构建策略网络

        这是一个抽象方法，需要在子类中实现具体的策略网络构建逻辑。
        不同的算法会有不同的策略网络结构。

        Returns:
            Module: 策略网络模块
        """
        raise NotImplementedError

    def _build_learner(self, *args):
        """
        构建学习器

        根据配置创建学习器，包含优化器、损失函数等训练组件。

        Args:
            *args: 学习器构建所需的参数

        Returns:
            学习器实例
        """
        return REGISTRY_Learners[self.config.learner](*args)

    def action(self, observations):
        """
        根据观察选择动作

        这是一个抽象方法，需要在子类中实现具体的动作选择逻辑。

        Args:
            observations: 环境观察

        Returns:
            选择的动作
        """
        raise NotImplementedError

    def train(self, steps):
        """
        训练智能体

        这是一个抽象方法，需要在子类中实现具体的训练逻辑。

        Args:
            steps: 训练步数
        """
        raise NotImplementedError

    def test(self, env_fn, steps):
        """
        测试智能体

        这是一个抽象方法，需要在子类中实现具体的测试逻辑。

        Args:
            env_fn: 环境创建函数
            steps: 测试步数
        """
        raise NotImplementedError

    def finish(self):
        """
        清理资源和结束训练

        关闭日志记录器，清理分布式训练资源，确保程序正常退出。
        """
        # 关闭日志记录器
        if self.use_wandb:
            wandb.finish()  # 结束WandB会话
        else:
            self.writer.close()  # 关闭TensorBoard写入器

        # 清理分布式训练资源
        if self.distributed_training:
            if dist.get_rank() == 0:
                # 清理快照文件（如果存在）
                if os.path.exists(self.learner.snapshot_path):
                    if os.path.exists(os.path.join(self.learner.snapshot_path, "snapshot.pt")):
                        os.remove(os.path.join(self.learner.snapshot_path, "snapshot.pt"))
                    os.removedirs(self.learner.snapshot_path)
            # 销毁分布式进程组
            destroy_process_group()
