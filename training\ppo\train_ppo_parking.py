#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ============================================================================
# 在导入任何其他模块之前先设置警告过滤
# ============================================================================
import warnings
import os

# 禁用所有警告
warnings.filterwarnings('ignore')

# 特别是禁用 gymnasium 的警告
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

"""
PPO停车环境训练脚本

该脚本实现了使用PPO算法训练停车环境的智能体。
支持训练、测试和基准测试三种模式。
"""

import argparse
import numpy as np
from copy import deepcopy
from algorithms.utils import get_configs, recursive_dict_update
from algorithms.environments import make_envs
from algorithms.agents.utils.operations import set_seed
from algorithms.agents.agents import PPOCLIP_Agent


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser("PPO停车环境训练程序")
    parser.add_argument("--env-id", type=str, default="parking-v0",
                       help="环境ID")
    parser.add_argument("--test", type=int, default=0,
                       help="是否运行测试模式 (0: 否, 1: 是)")
    parser.add_argument("--benchmark", type=int, default=1,
                       help="是否运行基准测试模式 (0: 否, 1: 是)")

    return parser.parse_args()


if __name__ == "__main__":
    # ============================================================================
    # 主程序入口
    # ============================================================================

    # 解析命令行参数
    parser = parse_args()

    # 加载配置文件
    configs_dict = get_configs(file_dir="ppo_configs/train_ppo_parking.yaml")
    # 用命令行参数更新配置
    configs_dict = recursive_dict_update(configs_dict, parser.__dict__)
    configs = argparse.Namespace(**configs_dict)

    # 初始化环境和智能体
    set_seed(configs.seed)  # 设置随机种子
    envs = make_envs(configs)  # 创建环境
    Agent = PPOCLIP_Agent(config=configs, envs=envs)  # 创建PPO智能体

    # 打印训练信息
    train_information = {
        "深度学习框架": configs.dl_toolbox,
        "计算设备": configs.device,
        "算法": configs.agent,
        "环境名称": configs.env_name,
        "环境ID": configs.env_id
    }
    for k, v in train_information.items():
        print(f"{k}: {v}")

    # ============================================================================
    # 运行模式选择
    # ============================================================================

    if configs.benchmark:
        # 基准测试模式
        print("\n=== 基准测试模式 ===")

        def env_fn():  # 定义测试环境函数
            configs_test = deepcopy(configs)
            configs_test.parallels = configs_test.test_episode
            return make_envs(configs_test)

        # 计算训练参数
        train_steps = configs.running_steps // configs.parallels
        eval_interval = configs.eval_interval // configs.parallels
        test_episode = configs.test_episode
        num_epoch = int(train_steps / eval_interval)

        print(f"训练轮数: {num_epoch}")
        print(f"每轮步数: {eval_interval}")
        print(f"测试回合: {test_episode}")

        # 初始测试
        test_scores = Agent.test(env_fn, test_episode)
        Agent.save_model(model_name="best_model.pth")
        best_scores_info = {
            "mean": np.mean(test_scores),
            "std": np.std(test_scores),
            "step": Agent.current_step
        }
        print(f"初始测试分数: {best_scores_info['mean']:.2f} ± {best_scores_info['std']:.2f}")

        # 训练循环
        for i_epoch in range(num_epoch):
            print(f"\n轮次: {i_epoch + 1}/{num_epoch}")
            Agent.train(eval_interval)  # 训练智能体
            test_scores = Agent.test(env_fn, test_episode)  # 测试智能体

            current_mean = np.mean(test_scores)
            current_std = np.std(test_scores)
            print(f"当前测试分数: {current_mean:.2f} ± {current_std:.2f}")

            # 如果当前分数更好，保存最佳模型
            if current_mean > best_scores_info["mean"]:
                best_scores_info = {
                    "mean": current_mean,
                    "std": current_std,
                    "step": Agent.current_step
                }
                Agent.save_model(model_name="best_model.pth")  # 保存最佳模型
                print("发现更好的模型，已保存！")

        # 基准测试结束
        print(f"\n=== 基准测试完成 ===")
        print(f"最佳模型分数: {best_scores_info['mean']:.2f} ± {best_scores_info['std']:.2f}")
        print(f"最佳模型步数: {best_scores_info['step']}")
    else:
        if configs.test:
            # 测试模式
            print("\n=== 测试模式 ===")

            def env_fn():
                configs.parallels = configs.test_episode
                return make_envs(configs)

            print(f"从以下路径加载模型: {Agent.model_dir_load}")
            Agent.load_model(path=Agent.model_dir_load)  # 加载训练好的模型
            scores = Agent.test(env_fn, configs.test_episode)  # 测试模型

            mean_score = np.mean(scores)
            std_score = np.std(scores)
            print(f"测试结果:")
            print(f"平均分数: {mean_score:.2f}")
            print(f"标准差: {std_score:.2f}")
            print(f"最高分数: {np.max(scores):.2f}")
            print(f"最低分数: {np.min(scores):.2f}")
            print("测试完成。")
        else:
            # 训练模式
            print("\n=== 训练模式 ===")
            print(f"开始训练，总步数: {configs.running_steps}")

            Agent.train(configs.running_steps // configs.parallels)  # 训练智能体
            Agent.save_model("final_train_model.pth")  # 保存最终模型
            print("训练完成！")

    # 清理资源
    Agent.finish()