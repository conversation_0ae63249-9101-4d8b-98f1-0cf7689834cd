from algorithms.environments.single_agent_env.gym import Gym_Env
from algorithms.environments.utils import EnvironmentDict
from algorithms.utils import Optional

REGISTRY_ENV: Optional[EnvironmentDict] = {
    "Classic Control": Gym_Env,
    "Box2D": Gym_Env,
}

try:
    from algorithms.environments.single_agent_env.custom_parking import CustomParking_Env
    REGISTRY_ENV['custom_parking'] = CustomParking_Env  # 添加自定义环境
except Exception as error:
    REGISTRY_ENV["custom_parking"] = str(error)

__all__ = [
    "REGISTRY_ENV",
]