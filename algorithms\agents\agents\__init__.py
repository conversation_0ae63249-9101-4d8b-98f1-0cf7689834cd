from .base.callback import <PERSON><PERSON>allback, MultiAgentBaseCallback
from .base import Agent, MARLAgents, RandomAgents
from .core import OnPolicyAgent, OnPolicyMARLAgents

'''Single-Agent Reinforcement Learning algorithms'''
from .policy_gradient import PPOCLIP_Agent


'''Multi-Agent Reinforcement Learning Algorithms'''
from .multi_agent_rl import IPPO_Agents
from .multi_agent_rl import MAPPO_Agents


REGISTRY_Agents = {
    "PPO_Clip": PP<PERSON><PERSON><PERSON>_Agent,
    "RANDOM": RandomAgents,
    "IPPO": IPPO_Agents,
    "MAPPO": MAPPO_Agents
}

__all__ = [
    "BaseCallback", "Agent", "MARLAgents", "RandomAgents",

    "OnPolicyAgent",  "OnPolicyMARLAgents",

    "REGISTRY_Agents",

    "PPOCLIP_Agent",

    "IPPO_Agents", "MAPPO_Agents",

]
