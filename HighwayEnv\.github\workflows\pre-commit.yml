# https://pre-commit.com
# This GitHub Action assumes that the repo contains a valid .pre-commit-config.yaml file.
name: pre-commit
on:
  pull_request:
  push:
    branches: [master]

permissions:
  contents: read # to fetch code (actions/checkout)

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
      - run: python -m pip install pre-commit
      - run: python -m pre_commit --version
      - run: python -m pre_commit install
      - run: python -m pre_commit run --all-files
