[metadata]
name=highway-env
version=attr: my_package.__version__
author=<PERSON><PERSON>
author_email=<EMAIL>
description=An environment for simulated highway driving tasks.
long_description=file:README.md
long_description_content_type=text/markdown
url=https://github.com/eleurent/highway-env
license=MIT
require-python= >= 3.8
classifiers=
    Development Status :: 5 - Production/Stable
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
    License :: OSI Approved :: MIT License


[options]
setup_requires=
    pytest-runner
install_requires=
    farama-notifications
    gymnasium>=0.28
    numpy
    pygame>=2.0.2
    matplotlib
    pandas
    scipy
packages=find:
tests_require=
    pytest

[options.extras_require]
deploy = pytest-runner; sphinx<1.7.3; sphinx_rtd_theme

[options.packages.find]
exclude =
    tests
    docs
    scripts

[aliases]
test=pytest
