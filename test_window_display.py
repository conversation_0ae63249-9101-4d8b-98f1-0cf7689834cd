#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试窗口显示优化
验证黑边问题是否得到解决
"""

import numpy as np
import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms/environments/single_agent_env'))

from custom_parking import CustomParkingEnv

class DisplayTestConfig:
    """显示测试配置类"""
    def __init__(self):
        # 基本环境配置
        self.env_seed = 42
        self.max_episode_steps = 50
        self.render_mode = 'human'
        
        # 截图相关配置（可选）
        self.auto_save_screenshots = False
        self.screenshot_dir = 'display_test_screenshots'
        
        # 其他配置
        self.collision_reward = -10

def test_window_display():
    """测试窗口显示效果"""
    print("=== 窗口显示优化测试 ===\n")
    
    # 创建配置
    config = DisplayTestConfig()
    
    print("窗口配置:")
    print("- 宽度: 800px")
    print("- 高度: 600px") 
    print("- 缩放: 12倍")
    print("- 固定摄像头视角")
    print("- 优化的渲染设置")
    print()
    
    # 创建环境
    env = CustomParkingEnv(config)
    
    try:
        print("正在启动环境...")
        
        # 重置环境
        obs, info = env.reset()
        print("环境已重置")
        print(f"路径点数量: {info.get('path_length', 0)}")
        
        # 渲染初始状态
        env.render()
        print("初始状态已渲染")
        
        print("\n请检查窗口显示:")
        print("1. 窗口大小是否合适？")
        print("2. 是否还有大的黑边？")
        print("3. 停车场是否充分利用了窗口空间？")
        print("4. 路径显示是否清晰？")
        print()
        
        # 运行几步来观察动态效果
        print("运行几步来观察动态效果...")
        for step in range(10):
            # 随机动作
            action = env.action_space.sample()
            
            # 执行动作
            obs, reward, terminated, truncated, info = env.step(action)
            
            # 渲染
            env.render()
            
            print(f"Step {step + 1}: reward={reward:.3f}")
            
            # 如果episode结束，跳出循环
            if terminated or truncated:
                print(f"Episode结束于第 {step + 1} 步")
                break
                
            # 短暂延迟以便观察
            time.sleep(0.5)
        
        print("\n测试完成！")
        print("如果黑边问题仍然存在，可能需要进一步调整配置。")
        
        # 等待用户确认
        input("\n按Enter键关闭环境...")
        
    except KeyboardInterrupt:
        print("用户中断测试")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    finally:
        env.close()
        print("环境已关闭")

def print_display_tips():
    """打印显示优化建议"""
    print("=== 显示优化建议 ===\n")
    
    print("如果仍有黑边问题，可以尝试以下调整:")
    print()
    
    print("1. 调整窗口尺寸:")
    print("   - 修改 screen_width 和 screen_height")
    print("   - 尝试不同的宽高比（如16:9, 4:3等）")
    print()
    
    print("2. 调整缩放比例:")
    print("   - 增加 scaling 值以放大显示内容")
    print("   - 减少 scaling 值以显示更大范围")
    print()
    
    print("3. 调整摄像头位置:")
    print("   - 修改 centering_position 来调整视角中心")
    print("   - 尝试 [0.5, 0.6] 或 [0.5, 0.4] 等不同位置")
    print()
    
    print("4. 检查系统显示设置:")
    print("   - 确保系统显示缩放为100%")
    print("   - 检查显示器分辨率设置")
    print()
    
    print("5. 如果使用远程桌面:")
    print("   - 远程桌面可能影响窗口显示")
    print("   - 尝试在本地运行测试")

if __name__ == "__main__":
    print_display_tips()
    print("\n" + "="*50 + "\n")
    
    # 询问是否运行测试
    response = input("是否运行窗口显示测试？(y/n): ").lower().strip()
    
    if response in ['y', 'yes', '是']:
        test_window_display()
    else:
        print("测试已取消")
        print("\n您可以直接修改 custom_parking.py 中的 default_config 方法来调整窗口设置。")
