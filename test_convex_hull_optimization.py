#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试凸包优化路径点的效果
验证路径点数量减少和路径质量保持
"""

import numpy as np
import sys
import os
import matplotlib.pyplot as plt

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms/environments/single_agent_env'))

from custom_parking import SimpleHybridAStar

def generate_dense_path(start, goal, num_points=20):
    """生成密集的路径点用于测试优化效果"""
    x1, y1, theta1 = start
    x2, y2, theta2 = goal

    path = []

    # 生成更多的中间点
    for i in range(num_points):
        ratio = i / (num_points - 1)

        # 使用三次贝塞尔曲线生成平滑但密集的路径
        # 控制点
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        control_distance = distance / 3

        p0 = np.array([x1, y1])
        p3 = np.array([x2, y2])
        p1 = p0 + control_distance * np.array([np.cos(theta1), np.sin(theta1)])
        p2 = p3 - control_distance * np.array([np.cos(theta2), np.sin(theta2)])

        # 贝塞尔曲线计算
        t = ratio
        pos = (1 - t) ** 3 * p0 + 3 * (1 - t) ** 2 * t * p1 + 3 * (1 - t) * t ** 2 * p2 + t ** 3 * p3

        # 计算朝向
        if i < num_points - 1:
            derivative = 3 * (1 - t) ** 2 * (p1 - p0) + 6 * (1 - t) * t * (p2 - p1) + 3 * t ** 2 * (p3 - p2)
            theta = np.arctan2(derivative[1], derivative[0])
        else:
            theta = theta2

        path.append((pos[0], pos[1], theta))

    return path

def test_convex_hull_optimization():
    """测试改进的路径优化效果"""

    planner = SimpleHybridAStar()

    # 测试场景：生成更复杂的路径来测试优化效果
    test_cases = [
        {
            "name": "长距离弧线路径优化测试",
            "start": (0, 0, 0),
            "goal": (25, 15, np.pi/3),
            "description": "测试长距离弧线路径的优化效果"
        },
        {
            "name": "复杂机动路径优化测试",
            "start": (0, 0, 0),
            "goal": (8, 12, np.pi),
            "description": "测试复杂机动路径的优化效果"
        },
        {
            "name": "超长距离路径优化测试",
            "start": (0, 0, 0),
            "goal": (30, 8, np.pi/4),
            "description": "测试超长距离路径的优化效果"
        }
    ]
    
    print("=== 凸包路径优化测试 ===\n")
    
    for i, case in enumerate(test_cases, 1):
        print(f"{i}. {case['name']}")
        print(f"   描述: {case['description']}")
        print(f"   起点: {case['start']}")
        print(f"   终点: {case['goal']}")
        
        # 生成原始路径
        obstacles = []  # 无障碍物测试
        original_path = planner.plan(case['start'], case['goal'], obstacles)
        
        if original_path:
            print(f"   原始路径点数: {len(original_path)}")
            
            # 手动测试优化（因为plan方法已经包含了优化）
            # 我们需要直接调用路径生成方法来获取未优化的路径
            unoptimized_path = generate_dense_path(case['start'], case['goal'])
            
            if unoptimized_path:
                print(f"   未优化路径点数: {len(unoptimized_path)}")
                
                # 应用凸包优化
                optimized_path = planner._optimize_path_with_convex_hull(unoptimized_path)
                
                if optimized_path:
                    print(f"   优化后路径点数: {len(optimized_path)}")
                    reduction_rate = (len(unoptimized_path) - len(optimized_path)) / len(unoptimized_path) * 100
                    print(f"   路径点减少率: {reduction_rate:.1f}%")
                    
                    # 验证起点和终点是否保留
                    start_preserved = np.linalg.norm([optimized_path[0][0] - case['start'][0], 
                                                    optimized_path[0][1] - case['start'][1]]) < 1.0
                    end_preserved = np.linalg.norm([optimized_path[-1][0] - case['goal'][0], 
                                                  optimized_path[-1][1] - case['goal'][1]]) < 1.0
                    
                    print(f"   起点保留: {'✓' if start_preserved else '✗'}")
                    print(f"   终点保留: {'✓' if end_preserved else '✗'}")
                    
                    # 计算路径长度变化
                    original_length = calculate_path_length(unoptimized_path)
                    optimized_length = calculate_path_length(optimized_path)
                    length_change = abs(optimized_length - original_length) / original_length * 100
                    
                    print(f"   原始路径长度: {original_length:.2f}m")
                    print(f"   优化路径长度: {optimized_length:.2f}m")
                    print(f"   长度变化: {length_change:.1f}%")
                    
                else:
                    print("   ✗ 凸包优化失败")
            else:
                print("   ✗ 无法生成未优化路径")
        else:
            print("   ✗ 路径规划失败")
        
        print()

def calculate_path_length(path):
    """计算路径总长度"""
    if not path or len(path) < 2:
        return 0.0
    
    total_length = 0.0
    for i in range(len(path) - 1):
        dx = path[i+1][0] - path[i][0]
        dy = path[i+1][1] - path[i][1]
        total_length += np.sqrt(dx*dx + dy*dy)
    
    return total_length

def visualize_path_optimization():
    """可视化路径优化效果"""
    print("=== 路径优化可视化 ===")
    
    planner = SimpleHybridAStar()
    
    # 生成一个测试路径
    start = (0, 0, 0)
    goal = (12, 8, np.pi/3)
    
    # 生成未优化的弧线路径
    unoptimized_path = planner._generate_arc_path(start, goal)
    
    if unoptimized_path and len(unoptimized_path) > 4:
        # 应用凸包优化
        optimized_path = planner._optimize_path_with_convex_hull(unoptimized_path)
        
        if optimized_path:
            print(f"原始路径点数: {len(unoptimized_path)}")
            print(f"优化后路径点数: {len(optimized_path)}")
            print(f"减少了 {len(unoptimized_path) - len(optimized_path)} 个路径点")
            
            # 打印路径点坐标对比
            print("\n原始路径点:")
            for i, point in enumerate(unoptimized_path):
                print(f"  {i}: ({point[0]:.2f}, {point[1]:.2f}, {np.degrees(point[2]):.1f}°)")
            
            print("\n优化后路径点:")
            for i, point in enumerate(optimized_path):
                print(f"  {i}: ({point[0]:.2f}, {point[1]:.2f}, {np.degrees(point[2]):.1f}°)")
        else:
            print("凸包优化失败")
    else:
        print("无法生成测试路径")

if __name__ == "__main__":
    test_convex_hull_optimization()
    print("\n" + "="*50 + "\n")
    visualize_path_optimization()
    
    print("\n凸包优化总结:")
    print("1. 减少了路径点数量，提高计算效率")
    print("2. 保留了路径的主要几何特征")
    print("3. 确保起点和终点被保留")
    print("4. 路径长度变化较小，保持了路径质量")
