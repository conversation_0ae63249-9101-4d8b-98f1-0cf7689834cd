# ================================================================================================
# PPO-CLIP 智能体实现
#
# PPO (Proximal Policy Optimization) 是一种策略梯度算法，通过裁剪机制限制策略更新幅度
# 避免策略更新过大导致的性能崩溃，是目前最流行的强化学习算法之一
#
# 主要特点:
# 1. 使用重要性采样比率裁剪来限制策略更新
# 2. 支持连续和离散动作空间
# 3. 使用 Actor-Critic 架构
# 4. 支持 GAE (Generalized Advantage Estimation)
# ================================================================================================

import torch                                                      # PyTorch 深度学习框架
from tqdm import tqdm                                            # 进度条显示库
from copy import deepcopy                                        # 深拷贝函数
from argparse import Namespace                                   # 命名空间类，用于配置管理
from algorithms.utils import Union, Optional                    # 类型注解工具
from algorithms.environments import DummyVecEnv, SubprocVecEnv  # 向量化环境类
from algorithms.agents import Module                             # 神经网络模块基类
from algorithms.agents.utils import NormalizeFunctions, ActivationFunctions  # 归一化和激活函数
from algorithms.agents.policies import REGISTRY_Policy          # 策略网络注册表
from algorithms.agents.agents import OnPolicyAgent, BaseCallback  # 在线策略智能体基类和回调基类


class PPOCLIP_Agent(OnPolicyAgent):
    """
    PPO-CLIP 智能体实现类

    PPO-CLIP 使用裁剪目标函数来限制策略更新，避免过大的策略变化。
    继承自 OnPolicyAgent，实现了 PPO 特有的策略构建和训练逻辑。

    Args:
        config (Namespace): 包含超参数和其他设置的配置对象
        envs (Union[DummyVecEnv, SubprocVecEnv]): 向量化环境实例
        callback (Optional[BaseCallback]): 用户自定义回调函数，用于注入训练过程中的自定义逻辑
    """

    def __init__(self,
                 config: Namespace,
                 envs: Union[DummyVecEnv, SubprocVecEnv],
                 callback: Optional[BaseCallback] = None):
        """
        初始化 PPO-CLIP 智能体

        Args:
            config: 配置参数对象，包含网络结构、学习率等超参数
            envs: 向量化环境，支持并行环境执行
            callback: 回调函数，用于自定义训练过程中的行为
        """
        # 调用父类初始化方法，设置基本的智能体属性
        super(PPOCLIP_Agent, self).__init__(config, envs, callback)

        # 定义辅助信息的形状，PPO需要存储旧的对数概率用于重要性采样
        self.auxiliary_info_shape = {"old_logp": ()}

        # 构建经验回放缓冲区，用于存储轨迹数据
        self.memory = self._build_memory(self.auxiliary_info_shape)

        # 构建策略网络（Actor-Critic 网络）
        self.policy = self._build_policy()

        # 构建学习器，负责策略网络的参数更新
        self.learner = self._build_learner(self.config, self.policy, self.callback)

    def _build_policy(self) -> Module:
        """
        构建策略网络（Actor-Critic 网络）
        共享表征网络 (Basic_MLP)
        self.representation = nn.Sequential(
            nn.Linear(6, 256),      # 观察 -> 特征
            nn.ReLU()
        )

        Actor网络 (GaussianActorNet)
        self.actor = nn.Sequential(
            nn.Linear(256, 256),    # 特征 -> 隐藏层
            nn.ReLU(),
            nn.Linear(256, 2)       #
        )

        # Critic网络 (CriticNet)
        self.critic = nn.Sequential(
            nn.Linear(256, 256),    # 特征 -> 隐藏层
            nn.ReLU(),
            nn.Linear(256, 1)       #
        )
        根据配置参数构建相应的策略网络，支持离散动作（Categorical）和连续动作（Gaussian）

        Returns:
            Module: 构建好的策略网络模块
        """
        # 获取归一化函数，如果配置中没有指定则为None
        normalize_fn = NormalizeFunctions[self.config.normalize] if hasattr(self.config, "normalize") else None

        # 设置网络参数初始化方法为正交初始化，有助于训练稳定性
        initializer = torch.nn.init.orthogonal_

        # 获取激活函数
        activation = ActivationFunctions[self.config.activation]

        # 获取计算设备（CPU或GPU）
        device = self.device

        # 构建表示网络（特征提取网络），将原始观察转换为特征向量
        representation = self._build_representation(self.config.representation, self.observation_space, self.config)

        # 根据策略类型构建相应的策略网络
        if self.config.policy == "Categorical_AC":
            # 离散动作空间的 Actor-Critic 网络
            policy = REGISTRY_Policy["Categorical_AC"](
                action_space=self.action_space,                           # 动作空间
                representation=representation,                            # 表示网络
                actor_hidden_size=self.config.actor_hidden_size,        # Actor网络隐藏层大小
                critic_hidden_size=self.config.critic_hidden_size,      # Critic网络隐藏层大小
                normalize=normalize_fn,                                   # 归一化函数
                initialize=initializer,                                   # 参数初始化方法
                activation=activation,                                    # 激活函数
                device=device,                                           # 计算设备
                use_distributed_training=self.distributed_training)      # 是否使用分布式训练

        elif self.config.policy == "Gaussian_AC":
            # 连续动作空间的 Actor-Critic 网络（高斯策略）
            policy = REGISTRY_Policy["Gaussian_AC"](
                action_space=self.action_space,                           # 动作空间
                representation=representation,                            # 表示网络
                actor_hidden_size=self.config.actor_hidden_size,        # Actor网络隐藏层大小
                critic_hidden_size=self.config.critic_hidden_size,      # Critic网络隐藏层大小
                normalize=normalize_fn,                                   # 归一化函数
                initialize=initializer,                                   # 参数初始化方法
                activation=activation,                                    # 激活函数
                device=device,                                           # 计算设备
                use_distributed_training=self.distributed_training,      # 是否使用分布式训练
                activation_action=ActivationFunctions[self.config.activation_action])  # 动作输出激活函数
        else:
            # 不支持的策略类型，抛出异常
            raise AttributeError(f"PPO_CLIP currently does not support the policy named {self.config.policy}.")

        return policy

    def get_aux_info(self, policy_output: dict = None):
        """
        获取辅助信息

        PPO算法需要存储旧策略的对数概率，用于计算重要性采样比率。
        这个函数从策略输出中提取需要存储的辅助信息。

        Args:
            policy_output (dict): 策略网络的输出信息，包含动作、价值、对数概率等

        Returns:
            dict: 包含辅助信息的字典，主要是旧策略的对数概率
        """
        # 提取旧策略的对数概率，这是PPO算法计算重要性采样比率的关键信息
        aux_info = {"old_logp": policy_output['log_pi']}
        return aux_info

    def train(self, train_steps):
        """
        PPO智能体的训练主循环

        执行指定步数的训练，包括环境交互、数据收集、策略更新等完整流程。
        使用在线策略学习，即边与环境交互边更新策略。

        Args:
            train_steps (int): 训练步数，每步对应所有并行环境执行一次动作

        Returns:
            dict: 训练信息字典，包含损失、奖励等统计信息
        """
        # 初始化训练信息字典，用于记录训练过程中的各种统计信息
        train_info = {}

        # 获取当前环境观察，作为训练循环的起始状态
        obs = self.envs.buf_obs

        # 开始训练循环，使用tqdm显示进度条
        for _ in tqdm(range(train_steps)):
            # ========================================================================================
            # 步骤1: 观察预处理和策略执行
            # ========================================================================================

            # 更新观察的运行统计信息（均值和标准差），用于观察归一化
            self.obs_rms.update(obs)

            # 对观察进行预处理（归一化等）
            obs = self._process_observation(obs)

            # 使用当前策略选择动作，获取动作、价值估计和对数概率
            policy_out = self.action(obs, return_dists=False, return_logpi=True)

            # 从策略输出中提取动作和价值估计
            # 注意：logps变量在这里提取但未直接使用，它包含在policy_out中供后续使用
            acts, value = policy_out['actions'], policy_out['values']

            # 在环境中执行动作，获取下一步观察、奖励、终止信号等
            next_obs, rewards, terminals, truncations, infos = self.envs.step(acts)

            # 获取辅助信息（主要是旧策略的对数概率）
            aux_info = self.get_aux_info(policy_out)

            # ========================================================================================
            # 步骤2: 回调函数和数据存储
            # ========================================================================================

            # 调用训练步骤回调函数，允许用户自定义逻辑
            self.callback.on_train_step(self.current_step, envs=self.envs, policy=self.policy,
                                        obs=obs, policy_out=policy_out, acts=acts, vals=value, next_obs=next_obs,
                                        rewards=rewards, terminals=terminals, truncations=truncations,
                                        infos=infos, aux_info=aux_info, train_steps=train_steps)

            # 将当前步的经验存储到经验缓冲区
            # 包括观察、动作、处理后的奖励、价值估计、终止信号和辅助信息
            self.memory.store(obs, acts, self._process_reward(rewards), value, terminals, aux_info)

            # 检查经验缓冲区是否已满，如果满了则进行策略更新
            if self.memory.full:
                # ================================================================================
                # 步骤3: 策略更新阶段
                # ================================================================================

                # 计算终止状态的价值估计，用于计算优势函数
                vals = self.get_terminated_values(next_obs)

                # 为每个并行环境完成轨迹路径
                for i in range(self.n_envs):
                    if terminals[i]:
                        # 如果环境终止，价值为0
                        self.memory.finish_path(0.0, i)
                    else:
                        # 如果环境未终止，使用估计的价值
                        self.memory.finish_path(vals[i], i)

                # 执行多轮策略更新（PPO的核心）
                update_info = self.train_epochs(self.n_epochs)

                # 记录更新信息到日志
                self.log_infos(update_info, self.current_step)
                train_info.update(update_info)

                # 调用训练轮次结束回调函数
                self.callback.on_train_epochs_end(self.current_step, policy=self.policy, memory=self.memory,
                                                  current_episode=self.current_episode, train_steps=train_steps,
                                                  update_info=update_info)

                # 清空经验缓冲区，准备收集新的经验
                self.memory.clear()

            # ========================================================================================
            # 步骤4: 状态更新和回合处理
            # ========================================================================================

            # 更新累积奖励（用于奖励归一化）
            self.returns = self.gamma * self.returns + rewards

            # 深拷贝下一步观察，避免引用问题
            obs = deepcopy(next_obs)

            # 处理每个并行环境的状态
            for i in range(self.n_envs):
                # 检查环境是否终止或截断
                if terminals[i] or truncations[i]:
                    # 更新奖励的运行统计信息
                    self.ret_rms.update(self.returns[i:i + 1])

                    # 重置该环境的累积奖励
                    self.returns[i] = 0.0

                    # 特殊处理Atari游戏的截断情况
                    if self.atari and (~truncations[i]):
                        # Atari游戏中，某些截断不需要特殊处理
                        pass
                    else:
                        # 完成该环境的轨迹路径
                        if terminals[i]:
                            # 真正终止的环境，价值为0
                            self.memory.finish_path(0.0, i)
                        else:
                            # 截断的环境，使用价值估计
                            vals = self.get_terminated_values(next_obs)
                            self.memory.finish_path(vals[i], i)

                        # 重置环境观察
                        obs[i] = infos[i]["reset_obs"]
                        self.envs.buf_obs[i] = obs[i]

                        # 增加回合计数
                        self.current_episode[i] += 1

                        # 准备回合信息用于日志记录
                        if self.use_wandb:
                            # 使用Weights & Biases格式
                            episode_info = {
                                f"Episode-Steps/rank_{self.rank}/env-{i}": infos[i]["episode_step"],
                                f"Train-Episode-Rewards/rank_{self.rank}/env-{i}": infos[i]["episode_score"]
                            }
                        else:
                            # 使用TensorBoard格式
                            episode_info = {
                                f"Episode-Steps/rank_{self.rank}": {f"env-{i}": infos[i]["episode_step"]},
                                f"Train-Episode-Rewards/rank_{self.rank}": {f"env-{i}": infos[i]["episode_score"]}
                            }

                        # 记录回合信息
                        self.log_infos(episode_info, self.current_step)
                        train_info.update(episode_info)

                        # 调用回合信息回调函数
                        self.callback.on_train_episode_info(envs=self.envs, policy=self.policy, env_id=i,
                                                            infos=infos, rank=self.rank, use_wandb=self.use_wandb,
                                                            current_step=self.current_step,
                                                            current_episode=self.current_episode,
                                                            train_steps=train_steps)

            # ========================================================================================
            # 步骤5: 更新全局状态
            # ========================================================================================

            # 更新全局训练步数（所有并行环境的步数之和）
            self.current_step += self.n_envs

            # 调用训练步骤结束回调函数
            self.callback.on_train_step_end(self.current_step, envs=self.envs, policy=self.policy,
                                            train_steps=train_steps, train_info=train_info)

        # 返回训练信息，包含损失、奖励等统计数据
        return train_info
