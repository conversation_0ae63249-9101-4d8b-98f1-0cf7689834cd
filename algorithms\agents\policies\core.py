# ================================================================================================
# 策略网络核心组件库
#
# 这个文件包含了强化学习中各种策略网络的基础组件：
# 1. Q网络 - 用于值函数估计
# 2. Actor网络 - 用于策略输出（确定性和随机性）
# 3. Critic网络 - 用于状态价值估计
# 4. 各种分布网络 - 支持离散和连续动作空间
#
# 这些组件是构建完整强化学习算法（如PPO、SAC、DQN等）的基础"零件"
# ================================================================================================

import torch                                    # PyTorch 深度学习框架
import torch.nn as nn                          # 神经网络模块
import torch.nn.functional as F                # 神经网络函数库
from gymnasium.spaces import Discrete          # 离散动作空间
from algorithms.utils import Sequence, Optional, Callable, Union, Dict  # 类型注解工具
from algorithms.agents import Tensor, Module   # 张量和模块基类
from algorithms.agents.utils import ModuleType, mlp_block, gru_block, lstm_block  # 网络构建工具
from algorithms.agents.utils import CategoricalDistribution, DiagGaussianDistribution, ActivatedDiagGaussianDistribution  # 概率分布


class BasicQhead(Module):
    """
    基础Q网络头部类

    用于构建Q网络并计算Q值，是值函数方法（如DQN）的核心组件。
    Q网络输入状态，输出每个动作的Q值（状态-动作价值函数）。

    Args:
        state_dim (int): 输入状态的维度
        n_actions (int): 离散动作的数量
        hidden_sizes (Sequence[int]): 全连接层的隐藏单元数列表
        normalize (Optional[ModuleType]): 层归一化函数，用于稳定训练
        initialize (Optional[Callable]): 参数初始化函数
        activation (Optional[ModuleType]): 每层的激活函数
        device (Optional[Union[str, int, torch.device]]): 计算设备（CPU/GPU）
    """

    def __init__(self,
                 state_dim: int,
                 n_actions: int,
                 hidden_sizes: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None):
        """
        初始化基础Q网络

        构建一个多层感知机(MLP)网络，输入状态特征，输出每个动作的Q值
        """
        super(BasicQhead, self).__init__()

        # 构建网络层列表
        layers_ = []
        input_shape = (state_dim,)

        # 逐层构建隐藏层
        for h in hidden_sizes:
            # 使用mlp_block工具函数构建单层MLP
            mlp, input_shape = mlp_block(input_shape[0], h, normalize, activation, initialize, device)
            layers_.extend(mlp)

        # 添加输出层：输出维度为动作数量，无激活函数和归一化
        layers_.extend(mlp_block(input_shape[0], n_actions, None, None, initialize, device)[0])

        # 将所有层组合成顺序模型
        self.model = nn.Sequential(*layers_)

    def forward(self, x: Tensor):
        """
        Q网络前向传播

        Args:
            x (Tensor): 输入状态张量，形状为 [batch_size, state_dim]

        Returns:
            Tensor: Q值张量，形状为 [batch_size, n_actions]，每个元素表示对应动作的Q值
        """
        return self.model(x)


class DuelQhead(Module):
    """
    A base class to build Q network and calculate the dueling Q values.

    Args:
        state_dim (int): The input state dimension.
        n_actions (int): The number of discrete actions.
        hidden_sizes: List of hidden units for fully connect layers.
        normalize (Optional[ModuleType]): The layer normalization over a minibatch of inputs.
        initialize (Optional[Callable[..., Tensor]]): The parameters initializer.
        activation (Optional[ModuleType]): The activation function for each layer.
        device (Optional[Union[str, int, torch.device]]): The calculating device.
    """

    def __init__(self,
                 state_dim: int,
                 n_actions: int,
                 hidden_sizes: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None):
        super(DuelQhead, self).__init__()
        v_layers = []
        input_shape = (state_dim,)
        for h in hidden_sizes:
            v_mlp, input_shape = mlp_block(input_shape[0], h // 2, normalize, activation, initialize, device)
            v_layers.extend(v_mlp)
        v_layers.extend(mlp_block(input_shape[0], 1, None, None, None, device)[0])
        a_layers = []
        input_shape = (state_dim,)
        for h in hidden_sizes:
            a_mlp, input_shape = mlp_block(input_shape[0], h // 2, normalize, activation, initialize, device)
            a_layers.extend(a_mlp)
        a_layers.extend(mlp_block(input_shape[0], n_actions, None, None, None, device)[0])
        self.a_model = nn.Sequential(*a_layers)
        self.v_model = nn.Sequential(*v_layers)

    def forward(self, x: Tensor):
        """
        Returns the dueling Q-values.
        Parameters:
            x (Tensor): The input tensor.

        Returns:
            q: The dueling Q-values.
        """
        v = self.v_model(x)
        a = self.a_model(x)
        q = v + (a - a.mean(dim=-1).unsqueeze(dim=-1))
        return q


class C51Qhead(Module):
    """
    A base class to build Q network and calculate the distributional Q values.

    Args:
        state_dim (int): The input state dimension.
        n_actions (int): The number of discrete actions.
        atom_num (int): The number of atoms.
        hidden_sizes: List of hidden units for fully connect layers.
        normalize (Optional[ModuleType]): The layer normalization over a minibatch of inputs.
        initialize (Optional[Callable[..., Tensor]]): The parameters initializer.
        activation (Optional[ModuleType]): The activation function for each layer.
        device (Optional[Union[str, int, torch.device]]): The calculating device.
    """

    def __init__(self,
                 state_dim: int,
                 n_actions: int,
                 atom_num: int,
                 hidden_sizes: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None):
        super(C51Qhead, self).__init__()
        self.n_actions = n_actions
        self.atom_num = atom_num
        layers = []
        input_shape = (state_dim,)
        for h in hidden_sizes:
            mlp, input_shape = mlp_block(input_shape[0], h, normalize, activation, initialize, device)
            layers.extend(mlp)
        layers.extend(mlp_block(input_shape[0], n_actions * atom_num, None, None, initialize, device)[0])
        self.model = nn.Sequential(*layers)

    def forward(self, x: Tensor):
        """
        Returns the discrete action distributions.
        Parameters:
            x (Tensor): The input tensor.
        Returns:
            dist_probs: The probability distribution of the discrete actions.
        """
        dist_logits = self.model(x).view(-1, self.n_actions, self.atom_num)
        dist_probs = F.softmax(dist_logits, dim=-1)
        return dist_probs


class QRDQNhead(Module):
    """
    A base class to build Q networks for QRDQN policy.

    Args:
        state_dim (int): The input state dimension.
        n_actions (int): The number of discrete actions.
        atom_num (int): The number of atoms.
        hidden_sizes: List of hidden units for fully connect layers.
        normalize (Optional[ModuleType]): The layer normalization over a minibatch of inputs.
        initialize (Optional[Callable[..., Tensor]]): The parameters initializer.
        activation (Optional[ModuleType]): The activation function for each layer.
        device (Optional[Union[str, int, torch.device]]): The calculating device.
    """
    def __init__(self,
                 state_dim: int,
                 n_actions: int,
                 atom_num: int,
                 hidden_sizes: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None):
        super(QRDQNhead, self).__init__()
        self.n_actions = n_actions
        self.atom_num = atom_num
        layers = []
        input_shape = (state_dim,)
        for h in hidden_sizes:
            mlp, input_shape = mlp_block(input_shape[0], h, normalize, activation, initialize, device)
            layers.extend(mlp)
        layers.extend(mlp_block(input_shape[0], n_actions * atom_num, None, None, None, device)[0])
        self.model = nn.Sequential(*layers)

    def forward(self, x: Tensor):
        """
        Returns the quantiles of the distribution.
        Parameters:
            x (Tensor): The input tensor.
        Returns:
            quantiles: The quantiles of the action distribution.
        """
        quantiles = self.model(x).view(-1, self.n_actions, self.atom_num)
        return quantiles


class BasicRecurrent(Module):
    """Build recurrent  neural network to calculate Q values."""

    def __init__(self, **kwargs):
        super(BasicRecurrent, self).__init__()
        self.lstm = False
        if kwargs["rnn"] == "GRU":
            output, _ = gru_block(kwargs["input_dim"],
                                  kwargs["recurrent_hidden_size"],
                                  kwargs["recurrent_layer_N"],
                                  kwargs["dropout"],
                                  kwargs["initialize"],
                                  kwargs["device"])
        elif kwargs["rnn"] == "LSTM":
            self.lstm = True
            output, _ = lstm_block(kwargs["input_dim"],
                                   kwargs["recurrent_hidden_size"],
                                   kwargs["recurrent_layer_N"],
                                   kwargs["dropout"],
                                   kwargs["initialize"],
                                   kwargs["device"])
        else:
            raise "Unknown recurrent module!"
        self.rnn_layer = output
        fc_layer = mlp_block(kwargs["recurrent_hidden_size"], kwargs["action_dim"], None, None, None, kwargs["device"])[
            0]
        self.model = nn.Sequential(*fc_layer)

    def forward(self, x: Tensor, h: Tensor, c: Tensor = None):
        """Returns the rnn hidden and Q-values via RNN networks."""
        self.rnn_layer.flatten_parameters()
        if self.lstm:
            output, (hn, cn) = self.rnn_layer(x, (h, c))
            return hn, cn, self.model(output)
        else:
            output, hn = self.rnn_layer(x, h)
            return hn, self.model(output)


class ActorNet(nn.Module):
    """
    确定性策略的Actor网络

    用于确定性策略算法（如DDPG），直接输出连续动作值。
    与随机策略不同，这个网络输出的是具体的动作值，而不是动作分布。

    Args:
        state_dim (int): 输入状态的维度
        action_dim (int): 连续动作空间的维度
        hidden_sizes (Sequence[int]): 全连接层的隐藏单元数列表
        normalize (Optional[ModuleType]): 层归一化函数
        initialize (Optional[Callable]): 参数初始化函数
        activation (Optional[ModuleType]): 隐藏层激活函数
        activation_action (Optional[ModuleType]): 输出层激活函数，用于限制动作范围
        device (Optional[Union[str, int, torch.device]]): 计算设备
    """

    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 hidden_sizes: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 activation_action: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None):
        """
        初始化确定性Actor网络

        构建一个MLP网络，输入状态，输出确定性的连续动作
        """
        super(ActorNet, self).__init__()

        # 构建网络层
        layers = []
        input_shape = (state_dim,)

        # 构建隐藏层
        for h in hidden_sizes:
            mlp, input_shape = mlp_block(input_shape[0], h, normalize, activation, initialize, device)
            layers.extend(mlp)

        # 构建输出层：输出动作维度，使用动作激活函数（如tanh限制范围）
        layers.extend(mlp_block(input_shape[0], action_dim, None, activation_action, initialize, device)[0])

        # 组合成完整网络
        self.model = nn.Sequential(*layers)

    def forward(self, x: Tensor, avail_actions: Optional[Tensor] = None):
        """
        Actor网络前向传播

        Args:
            x (Tensor): 输入状态张量，形状为 [batch_size, state_dim]
            avail_actions (Optional[Tensor]): 可用动作掩码，用于屏蔽不可用动作

        Returns:
            Tensor: 输出动作张量，形状为 [batch_size, action_dim]
        """
        # 通过网络计算动作logits
        logits = self.model(x)

        # 如果提供了动作掩码，将不可用动作的logits设为很小的值
        if avail_actions is not None:
            logits[avail_actions == 0] = -1e10

        return logits


class CategoricalActorNet(Module):
    """
    The actor network for categorical policy, which outputs a distribution over all discrete actions.

    Args:
        state_dim (int): The input state dimension.
        action_dim (int): The dimension of continuous action space.
        hidden_sizes (Sequence[int]): List of hidden units for fully connect layers.
        normalize (Optional[ModuleType]): The layer normalization over a minibatch of inputs.
        initialize (Optional[Callable[..., Tensor]]): The parameters initializer.
        activation (Optional[ModuleType]): The activation function for each layer.
        device (Optional[Union[str, int, torch.device]]): The calculating device.
    """

    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 hidden_sizes: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None):
        super(CategoricalActorNet, self).__init__()
        layers = []
        input_shape = (state_dim,)
        for h in hidden_sizes:
            mlp, input_shape = mlp_block(input_shape[0], h, normalize, activation, initialize, device)
            layers.extend(mlp)
        layers.extend(mlp_block(input_shape[0], action_dim, None, None, initialize, device)[0])
        self.model = nn.Sequential(*layers)
        self.dist = CategoricalDistribution(action_dim)

    def forward(self, x: Tensor, avail_actions: Optional[Tensor] = None):
        """
        Returns the stochastic distribution over all discrete actions.
        Parameters:
            x (Tensor): The input tensor.
            avail_actions (Optional[Tensor]): The actions mask values when use actions mask, default is None.

        Returns:
            self.dist: CategoricalDistribution(action_dim), a distribution over all discrete actions.
        """
        logits = self.model(x)
        if avail_actions is not None:
            logits[avail_actions == 0] = -1e10
        self.dist.set_param(logits=logits)
        return self.dist


class CategoricalActorNet_SAC(CategoricalActorNet):
    """
    The actor network for categorical policy in SAC-DIS, which outputs a distribution over all discrete actions.

    Args:
        state_dim (int): The input state dimension.
        action_dim (int): The dimension of continuous action space.
        hidden_sizes (Sequence[int]): List of hidden units for fully connect layers.
        normalize (Optional[ModuleType]): The layer normalization over a minibatch of inputs.
        initialize (Optional[Callable[..., Tensor]]): The parameters initializer.
        activation (Optional[ModuleType]): The activation function for each layer.
        device (Optional[Union[str, int, torch.device]]): The calculating device.
    """

    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 hidden_sizes: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None):
        super(CategoricalActorNet_SAC, self).__init__(state_dim, action_dim, hidden_sizes,
                                                      normalize, initialize, activation, device)
        self.output = nn.Softmax(dim=-1)

    def forward(self, x: Tensor, avail_actions: Optional[Tensor] = None):
        """
        Returns the stochastic distribution over all discrete actions.
        Parameters:
            x (Tensor): The input tensor.
            avail_actions (Optional[Tensor]): The actions mask values when use actions mask, default is None.

        Returns:
            self.dist: CategoricalDistribution(action_dim), a distribution over all discrete actions.
        """
        logits = self.model(x)
        if avail_actions is not None:
            logits[avail_actions == 0] = -1e10
        probs = self.output(logits)
        self.dist.set_param(probs=probs)
        return self.dist


class GaussianActorNet(Module):
    """
    高斯策略Actor网络 - PPO等算法的核心组件

    用于随机策略算法（如PPO、A2C），输出连续动作空间上的高斯分布。
    这是PPO算法中Actor部分的具体实现，与确定性Actor不同，它输出动作的概率分布。

    网络结构：
    - 输入：状态s
    - 输出：高斯分布 N(μ, σ²)，其中μ是均值，σ是标准差

    高斯分布的优势：
    1. 自然支持探索：通过方差控制探索程度
    2. 可微分：支持策略梯度算法
    3. 连续动作：适合连续控制任务

    Args:
        state_dim (int): 输入状态的维度
        action_dim (int): 连续动作空间的维度
        hidden_sizes (Sequence[int]): 全连接层的隐藏单元数列表
        normalize (Optional[ModuleType]): 层归一化函数
        initialize (Optional[Callable]): 参数初始化函数
        activation (Optional[ModuleType]): 隐藏层激活函数
        activation_action (Optional[ModuleType]): 输出层激活函数
        device (Optional[Union[str, int, torch.device]]): 计算设备
    """

    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 hidden_sizes: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 activation_action: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None):
        """
        初始化高斯Actor网络

        构建输出高斯分布参数的网络：均值网络μ和对数标准差参数logσ
        """
        super(GaussianActorNet, self).__init__()

        # 构建均值网络μ(s)
        layers = []
        input_shape = (state_dim,)

        # 构建隐藏层
        for h in hidden_sizes:
            mlp, input_shape = mlp_block(input_shape[0], h, normalize, activation, initialize, device)
            layers.extend(mlp)

        # 构建输出层：输出动作维度的均值
        layers.extend(mlp_block(input_shape[0], action_dim, None, activation_action, initialize, device)[0])
        self.mu = nn.Sequential(*layers)

        # 对数标准差参数：可学习参数，初始化为-1（对应std=exp(-1)≈0.37）
        # 使用对数形式确保标准差始终为正
        self.logstd = nn.Parameter(-torch.ones((action_dim,), device=device))

        # 高斯分布对象，用于采样和计算概率
        self.dist = DiagGaussianDistribution(action_dim)

    def forward(self, x: Tensor):
        """
        高斯Actor网络前向传播

        Args:
            x (Tensor): 输入状态张量，形状为 [batch_size, state_dim]

        Returns:
            DiagGaussianDistribution: 高斯分布对象，包含：
                - 均值μ：网络输出的动作均值
                - 标准差σ：exp(logstd)，确保为正值
                - 可用于采样动作和计算对数概率
        """
        # 计算动作均值μ(s)
        mu = self.mu(x)

        # 计算标准差σ = exp(logstd)，确保为正值
        std = self.logstd.exp()

        # 设置高斯分布参数并返回分布对象
        self.dist.set_param(mu, std)
        return self.dist


class CriticNet(Module):
    """
    Critic网络 - 价值函数估计器

    用于估计状态价值V(s)或状态-动作价值Q(s,a)。这是Actor-Critic算法中的关键组件，
    负责评估当前策略的好坏，为策略改进提供指导信号。

    网络结构：
    - 输入：状态s（用于V(s)）或状态-动作对(s,a)（用于Q(s,a)）
    - 输出：单个标量值，表示价值估计

    Args:
        input_dim (int): 输入维度（状态维度 或 状态维度+动作维度）
        hidden_sizes (Sequence[int]): 全连接层的隐藏单元数列表
        normalize (Optional[ModuleType]): 层归一化函数
        initialize (Optional[Callable]): 参数初始化函数
        activation (Optional[ModuleType]): 激活函数
        device (Optional[Union[str, int, torch.device]]): 计算设备
    """

    def __init__(self,
                 input_dim: int,
                 hidden_sizes: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None):
        """
        初始化Critic网络

        构建一个MLP网络，输入状态（或状态-动作对），输出价值估计
        """
        super(CriticNet, self).__init__()

        # 构建网络层
        layers = []
        input_shape = (input_dim,)

        # 构建隐藏层
        for h in hidden_sizes:
            mlp, input_shape = mlp_block(input_shape[0], h, normalize, activation, initialize, device)
            layers.extend(mlp)

        # 构建输出层：输出维度为1（单个价值），无激活函数和归一化
        layers.extend(mlp_block(input_shape[0], 1, None, None, initialize, device)[0])

        # 组合成完整网络
        self.model = nn.Sequential(*layers)

    def forward(self, x: Tensor):
        """
        Critic网络前向传播

        Args:
            x (Tensor): 输入张量
                       - 对于状态价值V(s)：形状为 [batch_size, state_dim]
                       - 对于动作价值Q(s,a)：形状为 [batch_size, state_dim + action_dim]

        Returns:
            Tensor: 价值估计张量，形状为 [batch_size, 1]
        """
        return self.model(x)


class GaussianActorNet_SAC(Module):
    """
    The actor network for Gaussian policy in SAC, which outputs a distribution over the continuous action space.

    Args:
        state_dim (int): The input state dimension.
        action_dim (int): The dimension of continuous action space.
        hidden_sizes (Sequence[int]): List of hidden units for fully connect layers.
        normalize (Optional[ModuleType]): The layer normalization over a minibatch of inputs.
        initialize (Optional[Callable[..., Tensor]]): The parameters initializer.
        activation (Optional[ModuleType]): The activation function for each layer.
        activation_action (Optional[ModuleType]): The activation of final layer to bound the actions.
        device (Optional[Union[str, int, torch.device]]): The calculating device.
    """

    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 hidden_sizes: Sequence[int],
                 normalize: Optional[ModuleType] = None,
                 initialize: Optional[Callable[..., Tensor]] = None,
                 activation: Optional[ModuleType] = None,
                 activation_action: Optional[ModuleType] = None,
                 device: Optional[Union[str, int, torch.device]] = None):
        super(GaussianActorNet_SAC, self).__init__()
        layers = []
        input_shape = (state_dim,)
        for h in hidden_sizes:
            mlp, input_shape = mlp_block(input_shape[0], h, normalize, activation, initialize, device)
            layers.extend(mlp)
        self.output = nn.Sequential(*layers)
        self.out_mu = nn.Linear(hidden_sizes[-1], action_dim, device=device)
        self.out_log_std = nn.Linear(hidden_sizes[-1], action_dim, device=device)
        self.dist = ActivatedDiagGaussianDistribution(action_dim, activation_action, device)

    def forward(self, x: Tensor):
        """
        Returns the stochastic distribution over the continuous action space.
        Parameters:
            x (Tensor): The input tensor.

        Returns:
            self.dist: A distribution over the continuous action space.
        """
        output = self.output(x)
        mu = self.out_mu(output)
        log_std = torch.clamp(self.out_log_std(output), -20, 2)
        std = log_std.exp()
        self.dist.set_param(mu, std)
        return self.dist


class VDN_mixer(nn.Module):
    """
    The value decomposition networks mixer. (Additivity)
    """

    def __init__(self):
        super(VDN_mixer, self).__init__()

    def forward(self, values_n, states=None):
        return values_n.sum(dim=1)


class QMIX_mixer(nn.Module):
    """
    The QMIX mixer. (Monotonicity)

    Args:
        dim_state (int): The dimension of global state.
        dim_hidden (int): The size of rach hidden layer.
        dim_hypernet_hidden (int): The size of rach hidden layer for hyper network.
        n_agents (int): The number of agents.
        device (Optional[Union[str, int, torch.device]]): The calculating device.
    """

    def __init__(self,
                 dim_state: Optional[int] = None,
                 dim_hidden: int = 32,
                 dim_hypernet_hidden: int = 32,
                 n_agents: int = 1,
                 device: Optional[Union[str, int, torch.device]] = None):
        super(QMIX_mixer, self).__init__()
        self.device = device
        self.dim_state = dim_state
        self.dim_hidden = dim_hidden
        self.dim_hypernet_hidden = dim_hypernet_hidden
        self.n_agents = n_agents
        # self.hyper_w_1 = nn.Linear(self.dim_state, self.dim_hidden * self.n_agents)
        # self.hyper_w_2 = nn.Linear(self.dim_state, self.dim_hidden)
        self.hyper_w_1 = nn.Sequential(nn.Linear(self.dim_state, self.dim_hypernet_hidden),
                                       nn.ReLU(),
                                       nn.Linear(self.dim_hypernet_hidden, self.dim_hidden * self.n_agents)).to(device)
        self.hyper_w_2 = nn.Sequential(nn.Linear(self.dim_state, self.dim_hypernet_hidden),
                                       nn.ReLU(),
                                       nn.Linear(self.dim_hypernet_hidden, self.dim_hidden)).to(device)

        self.hyper_b_1 = nn.Linear(self.dim_state, self.dim_hidden).to(device)
        self.hyper_b_2 = nn.Sequential(nn.Linear(self.dim_state, self.dim_hypernet_hidden),
                                       nn.ReLU(),
                                       nn.Linear(self.dim_hypernet_hidden, 1)).to(device)

    def forward(self, values_n, states):
        """
        Returns the total Q-values for multi-agent team.

        Parameters:
            values_n: The individual values for agents in team.
            states: The global states.

        Returns:
            q_tot: The total Q-values for the multi-agent team.
        """
        states = torch.as_tensor(states, dtype=torch.float32, device=self.device)
        states = states.reshape(-1, self.dim_state)
        agent_qs = values_n.reshape(-1, 1, self.n_agents)
        # First layer
        w_1 = torch.abs(self.hyper_w_1(states))
        w_1 = w_1.view(-1, self.n_agents, self.dim_hidden)
        b_1 = self.hyper_b_1(states)
        b_1 = b_1.view(-1, 1, self.dim_hidden)
        hidden = F.elu(torch.bmm(agent_qs, w_1) + b_1)
        # Second layer
        w_2 = torch.abs(self.hyper_w_2(states))
        w_2 = w_2.view(-1, self.dim_hidden, 1)
        b_2 = self.hyper_b_2(states)
        b_2 = b_2.view(-1, 1, 1)
        # Compute final output
        y = torch.bmm(hidden, w_2) + b_2
        # Reshape and return
        q_tot = y.view(-1, 1)
        return q_tot


class QMIX_FF_mixer(nn.Module):
    """
    The feedforward mixer without the constraints of monotonicity.
    """

    def __init__(self,
                 dim_state: int = 0,
                 dim_hidden: int = 32,
                 n_agents: int = 1,
                 device: Optional[Union[str, int, torch.device]] = None):
        super(QMIX_FF_mixer, self).__init__()
        self.device = device
        self.dim_state = dim_state
        self.dim_hidden = dim_hidden
        self.n_agents = n_agents
        self.dim_input = self.n_agents + self.dim_state
        self.ff_net = nn.Sequential(nn.Linear(self.dim_input, self.dim_hidden),
                                    nn.ReLU(),
                                    nn.Linear(self.dim_hidden, self.dim_hidden),
                                    nn.ReLU(),
                                    nn.Linear(self.dim_hidden, self.dim_hidden),
                                    nn.ReLU(),
                                    nn.Linear(self.dim_hidden, 1)).to(self.device)
        self.ff_net_bias = nn.Sequential(nn.Linear(self.dim_state, self.dim_hidden),
                                         nn.ReLU(),
                                         nn.Linear(self.dim_hidden, 1)).to(self.device)

    def forward(self, values_n, states=None):
        """
        Returns the feedforward total Q-values.

        Parameters:
            values_n: The individual Q-values.
            states: The global states.
        """
        states = states.reshape(-1, self.dim_state)
        agent_qs = values_n.view([-1, self.n_agents])
        inputs = torch.cat([agent_qs, states], dim=-1).to(self.device)
        out_put = self.ff_net(inputs)
        bias = self.ff_net_bias(states)
        y = out_put + bias
        q_tot = y.view([-1, 1])
        return q_tot


class QTRAN_base(nn.Module):
    """
    The basic QTRAN module.

    Args:
        dim_state (int): The dimension of the global state.
        action_space (Dict[str, Discrete]): The action space for all agents.
        dim_hidden (int): The dimension of the hidden layers.
        n_agents (int): The number of agents.
        dim_utility_hidden (int): The dimension of the utility hidden states.
        use_parameter_sharing (bool): Whether to use parameters sharing trick.
        device: Optional[Union[str, int, torch.device]]: The calculating device.
    """
    def __init__(self,
                 dim_state: int = 0,
                 action_space: Dict[str, Discrete] = None,
                 dim_hidden: int = 32,
                 n_agents: int = 1,
                 dim_utility_hidden: int = 1,
                 use_parameter_sharing: bool = False,
                 device: Optional[Union[str, int, torch.device]] = None):
        super(QTRAN_base, self).__init__()
        self.dim_state = dim_state
        self.action_space = action_space
        self.n_actions_list = [a_space.n for a_space in action_space.values()]
        self.n_actions_max = max(self.n_actions_list)
        self.dim_hidden = dim_hidden
        self.n_agents = n_agents
        self.use_parameter_sharing = use_parameter_sharing

        self.dim_q_input = self.dim_state + dim_utility_hidden + self.n_actions_max
        self.dim_v_input = self.dim_state

        self.Q_jt = nn.Sequential(nn.Linear(self.dim_q_input, self.dim_hidden),
                                  nn.ReLU(),
                                  nn.Linear(self.dim_hidden, self.dim_hidden),
                                  nn.ReLU(),
                                  nn.Linear(self.dim_hidden, 1)).to(device)
        self.V_jt = nn.Sequential(nn.Linear(self.dim_v_input, self.dim_hidden),
                                  nn.ReLU(),
                                  nn.Linear(self.dim_hidden, self.dim_hidden),
                                  nn.ReLU(),
                                  nn.Linear(self.dim_hidden, 1)).to(device)
        self.dim_ae_input = dim_utility_hidden + self.n_actions_max
        self.action_encoding = nn.Sequential(nn.Linear(self.dim_ae_input, self.dim_ae_input),
                                             nn.ReLU(),
                                             nn.Linear(self.dim_ae_input, self.dim_ae_input)).to(device)

    def forward(self, states: Tensor, hidden_state_inputs: Tensor, actions_onehot: Tensor):
        """
        Calculating the joint Q and V values.

        Parameters:
            states (Tensor): The global states.
            hidden_state_inputs (Tensor): The joint hidden states inputs for QTRAN network.
            actions_onehot (Tensor): The joint onehot actions for QTRAN network.

        Returns:
            q_jt (Tensor): The evaluated joint Q values.
            v_jt (Tensor): The evaluated joint V values.
        """
        h_state_action_input = torch.cat([hidden_state_inputs, actions_onehot], dim=-1)
        h_state_action_encode = self.action_encoding(h_state_action_input).reshape(-1, self.n_agents, self.dim_ae_input)
        h_state_action_encode = h_state_action_encode.sum(dim=1)  # Sum across agents
        input_q = torch.cat([states, h_state_action_encode], dim=-1)
        input_v = states
        q_jt = self.Q_jt(input_q)
        v_jt = self.V_jt(input_v)
        return q_jt, v_jt


class QTRAN_alt(nn.Module):
    """
    The basic QTRAN module.

    Parameters:
        dim_state (int): The dimension of the global state.
        action_space (Dict[str, Discrete]): The action space for all agents.
        dim_hidden (int): The dimension of the hidden layers.
        n_agents (int): The number of agents.
        dim_utility_hidden (int): The dimension of the utility hidden states.
        use_parameter_sharing (bool): Whether to use parameters sharing trick.
        device: Optional[Union[str, int, torch.device]]: The calculating device.
    """

    def __init__(self,
                 dim_state: int = 0,
                 action_space: Dict[str, Discrete] = None,
                 dim_hidden: int = 32,
                 n_agents: int = 1,
                 dim_utility_hidden: int = 1,
                 use_parameter_sharing: bool = False,
                 device: Optional[Union[str, int, torch.device]] = None):
        super(QTRAN_alt, self).__init__()
        self.dim_state = dim_state
        self.action_space = action_space
        self.n_actions_list = [a_space.n for a_space in action_space.values()]
        self.n_actions_max = max(self.n_actions_list)
        self.dim_hidden = dim_hidden
        self.n_agents = n_agents
        self.use_parameter_sharing = use_parameter_sharing
        self.device = device

        self.dim_q_input = self.dim_state + dim_utility_hidden + self.n_actions_max + self.n_agents
        self.dim_v_input = self.dim_state

        self.Q_jt = nn.Sequential(nn.Linear(self.dim_q_input, self.dim_hidden),
                                  nn.ReLU(),
                                  nn.Linear(self.dim_hidden, self.dim_hidden),
                                  nn.ReLU(),
                                  nn.Linear(self.dim_hidden, self.n_actions_max)).to(device)
        self.V_jt = nn.Sequential(nn.Linear(self.dim_v_input, self.dim_hidden),
                                  nn.ReLU(),
                                  nn.Linear(self.dim_hidden, self.dim_hidden),
                                  nn.ReLU(),
                                  nn.Linear(self.dim_hidden, 1)).to(device)
        self.dim_ae_input = dim_utility_hidden + self.n_actions_max
        self.action_encoding = nn.Sequential(nn.Linear(self.dim_ae_input, self.dim_ae_input),
                                             nn.ReLU(),
                                             nn.Linear(self.dim_ae_input, self.dim_ae_input)).to(device)

    def forward(self, states: Tensor, hidden_state_inputs: Tensor, actions_onehot: Tensor):
        """Calculating the joint Q and V values.

        Parameters:
            states (Tensor): The global states.
            hidden_state_inputs (Tensor): The joint hidden states inputs for QTRAN network.
            actions_onehot (Tensor): The joint onehot actions for QTRAN network.

        Returns:
            q_jt (Tensor): The evaluated joint Q values.
            v_jt (Tensor): The evaluated joint V values.
        """
        h_state_action_input = torch.cat([hidden_state_inputs, actions_onehot], dim=-1)
        h_state_action_encode = self.action_encoding(h_state_action_input).reshape(-1, self.n_agents, self.dim_ae_input)
        bs, dim_h = h_state_action_encode.shape[0], h_state_action_encode.shape[-1]
        agent_ids = torch.eye(self.n_agents, dtype=torch.float32, device=self.device)
        agent_masks = (1 - agent_ids)
        repeat_agent_ids = agent_ids.unsqueeze(0).repeat(bs, 1, 1)
        repeated_agent_masks = agent_masks.unsqueeze(0).unsqueeze(-1).repeat(bs, 1, 1, dim_h)
        repeated_h_state_action_encode = h_state_action_encode.unsqueeze(2).repeat(1, 1, self.n_agents, 1)
        h_state_action_encode = repeated_h_state_action_encode * repeated_agent_masks
        h_state_action_encode = h_state_action_encode.sum(dim=2)  # Sum across other agents

        repeated_states = states.unsqueeze(1).repeat(1, self.n_agents, 1)
        input_q = torch.cat([repeated_states, h_state_action_encode, repeat_agent_ids], dim=-1)
        input_v = states
        q_jt = self.Q_jt(input_q)
        v_jt = self.V_jt(input_v)
        return q_jt, v_jt
